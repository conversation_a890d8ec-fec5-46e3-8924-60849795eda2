import bg1 from '@/assets/img/all-images/bg/service-bg1.png';
import elements42 from '@/assets/img/elements/elements42.png';
import icon11 from '@/assets/img/icons/service-icon11.svg';
import icon12 from '@/assets/img/icons/service-icon12.svg';
import icon13 from '@/assets/img/icons/service-icon13.svg';
import icon14 from '@/assets/img/icons/service-icon14.svg';
import icon15 from '@/assets/img/icons/service-icon15.svg';
import icon16 from '@/assets/img/icons/service-icon16.svg';
import logo4 from '@/assets/img/icons/sub-logo4.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const service = [{
  image: icon11,
  title: 'Cyber Security Solution',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.'
}, {
  image: icon12,
  title: 'Scalable Cloud Solutions',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.'
}, {
  image: icon13,
  title: 'Data Protection Services',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.'
}, {
  image: icon14,
  title: 'Optimization Management',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.'
}, {
  image: icon15,
  title: 'HelpDesk 360 Solutions',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.'
}, {
  image: icon16,
  title: 'Software Development',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.'
}];
const Service = () => {
  return <>
      <div className="service4-section-area sp1" style={{
      backgroundImage: `url(${bg1.src})`,
      backgroundPosition: 'center',
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat'
    }}>
        <Image src={elements42} alt="" className="elements42" />
        <Container>
          <Row>
            <Col lg={12}>
              <div className="service-header space-margin60">
                <div className="heading6">
                  <h5>
                    <Image src={logo4} alt="" />
                    Our IT Solution Services
                  </h5>
                  <div className="space18" />
                  <h2 className="text-anime-style-3">
                    Tailored IT Services For <br className="d-lg-block d-none" /> Your Unique Business
                  </h2>
                </div>
                <div className="btn-area1">
                  <Link href="/service" className="vl-btn5">
                    <span className="demo">View More Services</span>
                    <span className="arrow">
                      <FaArrowRight className="fa-solid" />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
          <Row>
            {service.map((item, idx) => <Col key={idx} lg={4} md={6} data-aos="zoom-in" data-aos-duration={700 + idx * 100}>
                <div className="service-single-boxarea">
                  <div className="icons">
                    <Image src={item.image} alt="" />
                  </div>
                  <div className="space28" />
                  <div className="content-area">
                    <Link href="/service-single">{item.title}</Link>
                    <div className="space16" />
                    <p>{item.description}</p>
                    <div className="space24" />
                    <div className="btn-area">
                      <Link href="/service-single" className="service-btn">
                        Learn More
                      </Link>
                    </div>
                  </div>
                </div>
              </Col>)}
          </Row>
        </Container>
      </div>
    </>;
};
export default Service;