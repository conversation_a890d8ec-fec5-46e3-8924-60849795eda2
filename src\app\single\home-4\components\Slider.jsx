'use client';

import bg1 from '@/assets/img/all-images/bg/service-bg1.png';
import case5 from '@/assets/img/all-images/case/case-img5.png';
import case6 from '@/assets/img/all-images/case/case-img6.png';
import case7 from '@/assets/img/all-images/case/case-img7.png';
import case8 from '@/assets/img/all-images/case/case-img8.png';
import element41 from '@/assets/img/elements/elements41.png';
import logo4 from '@/assets/img/icons/sub-logo4.svg';
import Image from 'next/image';
import Link from 'next/link';
import { useRef } from 'react';
import { Col, Container, Row } from 'react-bootstrap';
import { FaAngleLeft, FaAngleRight, FaArrowRight } from 'react-icons/fa6';
import Slider from 'react-slick';
const slide = [{
  image: case7,
  title: '#CloudFlex Solution',
  description: 'Enhancing Productivity To with CloudFlex Solutions'
}, {
  image: case6,
  title: '#Network Optimization',
  description: 'Business Continuity with An BackupShield Data Recovery'
}, {
  image: case8,
  title: '#Cyber Security',
  description: 'Strengthening Security with A TechGuard Cybersecurity'
}, {
  image: case5,
  title: '#Cyber Security',
  description: 'Enhancing Productivity To with CloudFlex Solutions'
}];
const Sliders = () => {
  const sliderRef = useRef(null);
  const settings = {
    dots: false,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 2000,
    arrows: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    responsive: [{
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }]
  };
  return <>
      <div className="case4-section-area sp1" id="case" style={{
      backgroundImage: `url(${bg1.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Image src={element41} alt="" className="elements41" />
        <Container>
          <Row>
            <Col lg={5}>
              <div className="case-header space-margin60 heading6">
                <h5>
                  <Image src={logo4} alt="" />
                  Case Studies
                </h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">Delivering Results With Custom IT Solutions</h2>
              </div>
            </Col>
          </Row>
          <Row>
            <Col lg={12}>
              <div className="case4-slider owl-carousel">
                <Slider ref={sliderRef} {...settings}>
                  {slide.map((item, idx) => <div key={idx} className="case-single-boxarea">
                      <div className="img1  image-anime">
                        <Image src={item.image} alt="" />
                      </div>
                      <div className="content-area">
                        <Link href="#">
                          <span>{item.title}</span>
                        </Link>
                        <div className="space16" />
                        <Link href="#">{item.description}</Link>
                        <div className="arrow">
                          <Link href="#">
                            <FaArrowRight />
                          </Link>
                        </div>
                      </div>
                    </div>)}
                </Slider>
                <div className="owl-nav">
                  <button className="owl-prev" onClick={() => sliderRef.current?.slickPrev()}>
                    <FaAngleLeft />
                  </button>
                  <button className="owl-next" onClick={() => sliderRef.current?.slickNext()}>
                    <FaAngleRight />
                  </button>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Sliders;