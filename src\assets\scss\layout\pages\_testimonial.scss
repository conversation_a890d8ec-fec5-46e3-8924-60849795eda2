@use '../../utils/' as *;

/*============= TESTIMONIAL CSS AREA ===============*/
// Homepage 01 //
.testimonial1-section-area {
  position: relative;
  z-index: 1;
  .img1 {
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
      border-radius: 16px;
    }
  }
  .slider-galeria {
    .testimonial-slider-content-area {
      position: relative;
      z-index: 1;
      margin: 0;
      padding: 0 30px;
      .slick-slide {
        margin-right: 30px;
      }
      @media #{$xs} {
        margin: 30px 0 0 0;
      }
      @media #{$md} {
        margin: 30px 0 0 0;
      }
      .testimonial-author-area {
        position: relative;
        z-index: 1;
        background: var(--ztc-bg-bg-5);
        border-radius: 8px;
        padding: 28px 38px 28px 28px;
        .quito1 {
          position: absolute;
          right: 20px;
          top: 20px;
        }
        ul {
          li {
            display: inline-block;
            a {
              height: 26px;
              width: 26px;
              text-align: center;
              line-height: 26px;
              border-radius: 4px;
              display: inline-block;
              transition: all 0.4s;
              border-radius: 2px;
              background: rgba(255, 255, 255, 0.1);
              color: #ffa800;
            }
          }
        }
        p {
          color: var(--ztc-text-text-1);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s18);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 28px;
          transition: all 0.4s;
        }
        .elements18 {
          position: absolute;
          bottom: -35px;
        }
      }
      .testimonial-man-info-area {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .man-images-text {
          display: flex;
          align-items: center;
          .mans-img {
            img {
              height: 60px;
              width: 60px;
              text-align: center;
              line-height: 60px;
              border-radius: 50%;
            }
          }
          .man-text {
            padding-left: 16px;
            a {
              color: var(--ztc-text-text-2);
              font-family: var(--ztc-family-font1);
              font-size: var(--ztc-font-size-font-s20);
              font-style: normal;
              font-weight: var(--ztc-weight-semibold);
              line-height: 20px;
              display: inline-block;
              transition: all 0.4s;
            }
            p {
              color: var(--ztc-text-text-3);
              font-family: var(--ztc-family-font1);
              font-size: var(--ztc-font-size-font-s16);
              font-style: normal;
              font-weight: var(--ztc-weight-medium);
              line-height: 16px;
              transition: all 0.4s;
            }
          }
        }
      }
    }
  }
  .slider-galeria-thumbs {
    .active-thumb img {
      border: 2px solid #5b4bda;
      height: 70px;
      width: 70px !important;
      border-radius: 50%;
      object-fit: cover;
      display: block !important;
      // margin-bottom: 10px;
      box-sizing: border-box;
      cursor: pointer;

      .testimonial3-sliders-img {
        position: relative;
        &.slick-slide.slick-current.slick-active:after {
          position: absolute;
          content: '';
          height: 74px;
          width: 74px;
          background: var(--ztc-bg-bg-5);
          z-index: -1;
          top: -1px;
          left: -2px;
          border-radius: 50%;
        }
        // img {
        //   height: 70px;
        //   width: 70px;
        //   border-radius: 50%;
        //   object-fit: cover;
        //   transition: all 0.4s;
        //   margin: 0 0 10px 0;
        //   cursor: pointer;
        // }
      }
    }
  }
}

// Homepage 02 //
.testimonial2-section-area {
  position: relative;
  z-index: 1;
  // background-image: url(../../../img/all-images/bg/footer-bg2.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  .img1 {
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
      border-radius: 16px;
    }
  }
  .slider-galeria {
    .testimonial-slider-content-area {
      position: relative;
      z-index: 1;
      margin: 0;
      padding: 0 30px;
      .slick-slide {
        margin-right: 30px;
      }
      @media #{$xs} {
        margin: 30px 0 0 0;
      }
      @media #{$md} {
        margin: 30px 0 0 0;
      }
      .testimonial-author-area {
        position: relative;
        z-index: 1;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        padding: 28px 38px 28px 28px;
        .quito2 {
          position: absolute;
          right: 20px;
          top: 20px;
        }
        ul {
          li {
            display: inline-block;
            a {
              height: 26px;
              width: 26px;
              text-align: center;
              line-height: 26px;
              border-radius: 4px;
              display: inline-block;
              transition: all 0.4s;
              border-radius: 2px;
              background: rgba(255, 255, 255, 0.1);
              color: #ffa800;
            }
          }
        }
        p {
          color: var(--ztc-text-text-1);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s18);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 28px;
          transition: all 0.4s;
        }
        h4 {
          color: var(--ztc-text-text-1);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s20);
          font-style: italic;
          font-weight: var(--ztc-weight-medium);
          line-height: 20px;
          display: inline-block;
        }
        .elements21 {
          position: absolute;
          bottom: -30px;
        }
      }
      .testimonial-man-info-area {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .man-images-text {
          display: flex;
          align-items: center;
          .mans-img {
            img {
              height: 60px;
              width: 60px;
              text-align: center;
              line-height: 60px;
              border-radius: 50%;
            }
          }
          .man-text {
            padding-left: 16px;
            a {
              color: var(--ztc-text-text-1);
              font-family: var(--ztc-family-font1);
              font-size: var(--ztc-font-size-font-s20);
              font-style: normal;
              font-weight: var(--ztc-weight-semibold);
              line-height: 20px;
              display: inline-block;
              transition: all 0.4s;
            }
            p {
              color: var(--ztc-text-text-1);
              font-family: var(--ztc-family-font1);
              font-size: var(--ztc-font-size-font-s16);
              font-style: normal;
              font-weight: var(--ztc-weight-medium);
              line-height: 16px;
              transition: all 0.4s;
              opacity: 80%;
            }
          }
        }
        img.elements17 {
          filter: brightness(0) invert(1);
        }
      }
    }
  }
  .slider-galeria-thumbs {
    .active-thumb img {
      border: 2px solid #c0f037;
      height: 70px;
      width: 70px !important;
      border-radius: 50%;
      object-fit: cover;
      display: block !important;
      // margin-bottom: 10px;
      box-sizing: border-box;
      cursor: pointer;

      .testimonial3-sliders-img {
        position: relative;
        &.slick-slide.slick-current.slick-active:after {
          position: absolute;
          content: '';
          height: 74px;
          width: 74px;
          background: var(--ztc-bg-bg-5);
          z-index: -1;
          top: -1px;
          left: -2px;
          border-radius: 50%;
        }
        // img {
        //   height: 70px;
        //   width: 70px;
        //   border-radius: 50%;
        //   object-fit: cover;
        //   transition: all 0.4s;
        //   margin: 0 0 10px 0;
        //   cursor: pointer;
        // }
      }
    }
  }
}

// Homepage 04 //
.testimonial4-section-area {
  position: relative;
  z-index: 1;
  .testimonial4-slider {
    position: relative;
    z-index: 1;
    .slick-slide {
      padding-left: 15px;
      padding-right: 15px;
    }
    .owl-nav {
      position: absolute;
      top: -100px;
      right: 0;
      @media #{$xs} {
        position: relative;
        z-index: 1;
        top: 0;
        left: 0;
        text-align: center;
        margin-top: 30px;
      }
      @media #{$md} {
        position: relative;
        z-index: 1;
        top: 0;
        left: 0;
        text-align: center;
        margin-top: 30px;
      }
      button {
        height: 60px;
        width: 60px;
        text-align: center;
        line-height: 60px;
        display: inline-block;
        transition: all 0.4s;
        color: var(--ztc-text-text-12);
        background: var(--ztc-bg-bg-1);
        border-radius: 50%;
        font-size: var(--ztc-font-size-font-s20);
        border: transparent;
        &:hover {
          background: var(--ztc-bg-bg-10);
          transition: all 0.4s;
          color: var(--ztc-text-text-1);
        }
        &.owl-prev {
          margin: 0 16px 0 0;
        }
      }
    }
    .testimonial-review-box {
      position: relative;
      z-index: 1;
      border-radius: 8px;
      background: var(--ztc-bg-bg-1);
      padding: 32px 28px;
      .man-text {
        display: flex;
        align-items: center;
        .man {
          img {
            height: 60px;
            width: 60px;
            text-align: center;
            line-height: 60px;
            border-radius: 50%;
          }
        }
        .text {
          padding-left: 16px;
          a {
            color: var(--ztc-text-text-10);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s20);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 20px;
            display: inline-block;
            transition: all 0.4s;
            &:hover {
              color: var(--ztc-text-text-12);
              transition: all 0.4s;
            }
          }
          p {
            color: var(--ztc-text-text-11);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 16px;
            padding-bottom: 0;
            border: none;
          }
        }
      }
      p {
        color: var(--ztc-text-text-10);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-style: normal;
        font-weight: var(--ztc-weight-medium);
        line-height: 26px;
        opacity: 80%;
        padding-bottom: 24px;
        border-bottom: 1px solid #e6e6e8;
      }
      .logo-area {
        display: flex;
        align-items: center;
        justify-content: space-between;
        img {
          width: 85px;
          height: 28px;
          object-fit: contain;
        }
        ul {
          li {
            color: #ffa800;
            display: inline-block;
          }
        }
      }
    }
  }
}

// Homepage 05 //
.testimonial5-section-area {
  position: relative;
  z-index: 1;
  margin: 0 30px;
  border-radius: 16px;
  @media #{$xs} {
    margin: 0;
    border-radius: 0;
  }
  @media #{$md} {
    margin: 0;
    border-radius: 0;
  }
  .slick-slide {
    padding-right: 30px;
    box-sizing: border-box;
  }
  .elements46 {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
  }
  .testimonial4-slider {
    position: relative;
    z-index: 1;

    .owl-nav {
      position: absolute;
      top: -100px;
      right: 0;
      @media #{$xs} {
        position: relative;
        z-index: 1;
        top: 0;
        left: 0;
        text-align: center;
        margin-top: 30px;
      }
      @media #{$md} {
        position: relative;
        z-index: 1;
        top: 0;
        left: 0;
        text-align: center;
        margin-top: 30px;
      }
      button {
        height: 60px;
        width: 60px;
        text-align: center;
        line-height: 60px;
        display: inline-block;
        transition: all 0.4s;
        color: var(--ztc-text-text-13);
        background: var(--ztc-bg-bg-1);
        border-radius: 50%;
        font-size: var(--ztc-font-size-font-s20);
        border: transparent;
        &:hover {
          background: var(--ztc-bg-bg-12);
          transition: all 0.4s;
          color: var(--ztc-text-text-1);
        }
        &.owl-prev {
          margin: 0 16px 0 0;
        }
      }
    }
    .testimonial-review-box {
      position: relative;
      z-index: 1;
      border-radius: 8px;
      background: var(--ztc-bg-bg-1);
      padding: 32px 28px;
      .man-text {
        display: flex;
        align-items: center;
        .man {
          img {
            height: 60px;
            width: 60px;
            text-align: center;
            line-height: 60px;
            border-radius: 50%;
          }
        }
        .text {
          padding-left: 16px;
          a {
            color: var(--ztc-text-text-14);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s20);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 20px;
            display: inline-block;
            transition: all 0.4s;
            &:hover {
              color: var(--ztc-text-text-13);
              transition: all 0.4s;
            }
          }
          p {
            color: var(--ztc-text-text-15);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 16px;
            padding-bottom: 0;
            border: none;
          }
        }
      }
      p {
        color: var(--ztc-text-text-14);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-style: normal;
        font-weight: var(--ztc-weight-medium);
        line-height: 26px;
        opacity: 80%;
        padding-bottom: 24px;
        border-bottom: 1px solid #e6e6e8;
      }
      .logo-area {
        display: flex;
        align-items: center;
        justify-content: space-between;
        img {
          width: 85px;
          height: 28px;
          object-fit: contain;
        }
        ul {
          li {
            color: #ffa800;
            display: inline-block;
          }
        }
      }
    }
  }
}

// Inner Pages //
.testimonial-inner-section {
  position: relative;
  z-index: 1;
  .testimonial-review-box {
    position: relative;
    z-index: 1;
    border-radius: 8px;
    background: var(--ztc-bg-bg-1);
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.09);
    padding: 32px 28px;
    margin-bottom: 30px;
    .man-text {
      display: flex;
      align-items: center;
      .man {
        img {
          height: 60px;
          width: 60px;
          text-align: center;
          line-height: 60px;
          border-radius: 50%;
        }
      }
      .text {
        padding-left: 16px;
        a {
          color: var(--ztc-text-text-2);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s20);
          font-style: normal;
          font-weight: var(--ztc-weight-semibold);
          line-height: 20px;
          display: inline-block;
          transition: all 0.4s;
          &:hover {
            color: #2e0797;
            transition: all 0.4s;
          }
        }
        p {
          color: var(--ztc-text-text-3);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 16px;
          padding-bottom: 0;
          border: none;
        }
      }
    }
    p {
      color: var(--ztc-text-text-2);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s18);
      font-style: normal;
      font-weight: var(--ztc-weight-medium);
      line-height: 26px;
      opacity: 80%;
      padding-bottom: 24px;
      border-bottom: 1px solid #e6e6e8;
    }
    .logo-area {
      display: flex;
      align-items: center;
      justify-content: space-between;
      img {
        width: 85px;
        height: 28px;
        object-fit: contain;
      }
      ul {
        li {
          color: #ffa800;
          display: inline-block;
        }
      }
    }
  }
}
/*============= TESTIMONIAL CSS AREA ENDS ===============*/
