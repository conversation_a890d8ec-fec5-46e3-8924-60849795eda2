import blog10 from '@/assets/img/all-images/blog/blog-img10.png';
import blog8 from '@/assets/img/all-images/blog/blog-img8.png';
import blog9 from '@/assets/img/all-images/blog/blog-img9.png';
import element39 from '@/assets/img/elements/elements39.png';
import calender2 from '@/assets/img/icons/calender2.svg';
import logo4 from '@/assets/img/icons/sub-logo4.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const blog = [{
  image: blog8,
  description: 'How to Choose the Right BPO Partner for Your Best Business',
  date: '4 August 2024',
  duration: 900,
  class: 'fade-left'
}, {
  image: blog9,
  description: 'The Future of BPO: Trends On Shaping A Customer Support',
  date: '5 August 2024',
  duration: 1000,
  class: 'fade-right'
}, {
  image: blog10,
  description: 'From Data to Decisions: Using Analytic Call Centre Operation',
  date: '6 August 2024',
  duration: 1000,
  class: 'fade-left'
}];
const Blog = () => {
  return <>
      <div className="vl-blog-4-area sp2">
        <Image src={element39} alt="" className="elements39" />
        <Container>
          <Row>
            <Col lg={6} className="m-auto">
              <div className="vl-blog-1-section-box heading6 text-center space-margin60">
                <h5 className="vl-section-subtitle">
                  <span>
                    <Image src={logo4} alt="" />
                  </span>
                  Our Blog
                </h5>
                <div className="space20" />
                <h2 className="vl-section-title text-anime-style-3">Our Latest Blog &amp; News</h2>
              </div>
            </Col>
          </Row>
          <Row>
            {blog.map((item, idx) => <Col lg={4} md={6} key={idx} data-aos={item.class} data-aos-duration={item.duration}>
                <div className="vl-blog-1-item">
                  <div className="vl-blog-1-thumb image-anime">
                    <Image src={item.image} alt="" />
                  </div>
                  <div className="vl-blog-1-content">
                    <div className="vl-blog-meta">
                      <ul className='p-0 m-0'>
                        <li>
                          <Link href="">
                            <Image src={calender2} alt="" />
                            {item.date}
                          </Link>
                        </li>
                      </ul>
                    </div>
                    <div className="space16" />
                    <h4 className="vl-blog-1-title">
                      <Link href="/blog-single">{item.description}</Link>
                    </h4>
                    <div className="space20" />
                    <Link href="/blog-single" className="readmore">
                      Learn More <FaArrowRight />
                    </Link>
                  </div>
                </div>
              </Col>)}
          </Row>
        </Container>
      </div>
    </>;
};
export default Blog;