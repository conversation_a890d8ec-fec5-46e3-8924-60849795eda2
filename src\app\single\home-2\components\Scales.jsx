import bg1 from '@/assets/img/all-images/bg/footer-bg1.png';
import work3 from '@/assets/img/all-images/work/work-img3.png';
import work4 from '@/assets/img/all-images/work/work-img4.png';
import element20 from '@/assets/img/elements/elements20.png';
import element22 from '@/assets/img/elements/elements22.png';
import arrow1 from '@/assets/img/icons/arrow1.svg';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const Scales = () => {
  return <>
      <div className="work2-section-area sp1" id="work" style={{
      backgroundImage: `url(${bg1.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Container>
          <Row className="align-items-center">
            <Col lg={7}>
              <div className="soultions-images">
                <Row>
                  <Col lg={6} md={6}>
                    <div className="img1 ">
                      <Image src={work3} alt="" />
                    </div>
                    <div className="arrow-circle text-end">
                      <Link href="">
                        <Image src={element20} alt="" className="elements20 keyframe5" />
                        <Image src={arrow1} alt="" className="arrow1" />
                      </Link>
                    </div>
                  </Col>
                  <Col lg={6} md={6}>
                    <div className="img2">
                      <div className="star">
                        <Image src={element22} alt="" className="keyframe5" />
                      </div>
                      <div className="space100" />
                      <div className="img ">
                        <Image src={work4} alt="" />
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
            </Col>
            <Col lg={5}>
              <div className="solution-header heading4">
                <h5>
                  <span>
                    <Image src={logo2} alt="" />
                  </span>
                  How We Do
                </h5>
                <div className="space24" />
                <h2 className="text-anime-style-3">Customer Service That Scales A Your Business</h2>
                <div className="space16" />
                <p data-aos="fade-left" data-aos-duration={800}>
                  Business Process Outsourcing (BPO) streamline enhance customer interactions, understanding nuances effective.
                </p>
                <div className="space24" />
                <div className="bg-progress" data-aos="fade-left" data-aos-duration={900}>
                  <div className="progress-bar">
                    <label>
                      It Solution <span>98%</span>
                    </label>
                    <div className="progress">
                      <div className="progress-inner" style={{
                      width: '98%'
                    }} />
                    </div>
                  </div>
                  <div className="progress-bar">
                    <label>
                      Cyber Security <span>99%</span>
                    </label>
                    <div className="progress">
                      <div className="progress-inner" style={{
                      width: '99%'
                    }} />
                    </div>
                  </div>
                  <div className="progress-bar">
                    <label>
                      Cloud Solution <span>99%</span>
                    </label>
                    <div className="progress">
                      <div className="progress-inner" style={{
                      width: '99%'
                    }} />
                    </div>
                  </div>
                </div>
                <div className="space32" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1000}>
                  <Link href="/contact" className="vl-btn3">
                    <span className="demo">Boost Your Customer Support</span>
                    <span className="arrow">
                      <FaArrowRight />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Scales;