import about5 from '@/assets/img/all-images/about/about-img5.png';
import about6 from '@/assets/img/all-images/about/about-img6.png';
import about7 from '@/assets/img/all-images/about/about-img7.png';
import element27 from '@/assets/img/elements/elements27.png';
import element28 from '@/assets/img/elements/elements28.png';
import check3 from '@/assets/img/icons/check3.svg';
import logo3 from '@/assets/img/icons/sub-logo3.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
const About = () => {
  return <>
      <div className="about3-section-area sp1" id="about">
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <Row className="align-items-center">
                <Col lg={6} md={6}>
                  <div className="images">
                    <Image src={element27} alt="" className="elements27" />
                    <Image src={element28} alt="" className="elements28" />
                    <div className="img1 image-anime ">
                      <Image src={about5} alt="" />
                    </div>
                  </div>
                  <div className="space30 d-md-none d-block" />
                </Col>
                <Col lg={6} md={6}>
                  <div className="img1 image-anime ">
                    <Image src={about6} alt="" />
                  </div>
                  <div className="space30" />
                  <div className="img1 image-anime ">
                    <Image src={about7} alt="" />
                  </div>
                </Col>
              </Row>
            </Col>
            <Col lg={6}>
              <div className="about-header-area heading5">
                <h5 data-aos="fade-left" data-aos-duration={700}>
                  <Image src={logo3} alt="" />
                  About Web Circle Technology  help desk
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">Our Team Ready to Assist You Anytime, Anywheres</h2>
                <div className="space18" />
                <p data-aos="fade-left" data-aos-duration={800}>
                  Our Help Desk is staffed with experienced professionals who on are committed to providing you with the solutions you need, as
                  quickly and efficiently as possible. whether you're dealing with.
                </p>
                <div className="space16" />
                <Row data-aos="fade-left" data-aos-duration={900}>
                  <Col lg={4}>
                    <ul className="p-0">
                      <li>
                        <Image src={check3} alt="" />
                        Our Expert Team
                      </li>
                      <li>
                        <Image src={check3} alt="" />
                        24/7 Availability
                      </li>
                    </ul>
                  </Col>
                  <Col lg={6}>
                    <ul className="p-0">
                      <li>
                        <Image src={check3} alt="" />
                        Customer-Centric Approach
                      </li>
                      <li>
                        <Image src={check3} alt="" />
                        Comprehensive Knowledge
                      </li>
                    </ul>
                  </Col>
                </Row>
                <div className="space32" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1100}>
                  <Link href="/contact" className="vl-btn4">
                    Browse Help Topics
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default About;