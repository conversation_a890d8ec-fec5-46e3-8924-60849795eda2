import blog1 from '@/assets/img/all-images/blog/blog-img1.png';
import blog17 from '@/assets/img/all-images/blog/blog-img17.png';
import blog18 from '@/assets/img/all-images/blog/blog-img18.png';
import blog2 from '@/assets/img/all-images/blog/blog-img2.png';
import calender1 from '@/assets/img/icons/calender1.svg';
import user1 from '@/assets/img/icons/user1.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
import Pagination from '../../components/Pagination';
import Sidebar from './Sidebar';
const blog = [{
  image: blog1,
  name: '<PERSON>',
  title: 'Revolutionize Your Business Operations with Advanced IT Solution Technology',
  description: 'Fuel your business success with our custom IT services. We design solutions that are tailored to your an specific needs.'
}, {
  image: blog2,
  name: '<PERSON>',
  title: 'Transform Your Business Operations with Tailored IT Solutions Designed',
  description: 'Fuel your business success with our custom IT services. We design solutions that are tailored to your an specific needs.'
}, {
  image: blog17,
  name: '<PERSON>',
  title: 'Transform Your Business Operations with Tailored IT Solutions Designed',
  description: 'Digital transformation is not just a trend; it’s a necessity for businesses looking to thrive in a competitive landscape.'
}, {
  image: blog18,
  name: 'Rodger Struck',
  title: 'Unlock the Full Potential Your Company through Innovative Technology Expert',
  description: 'Every business is unique, an so are its IT needs. Our solution comprehensive IT services are tailored to fit your specific'
}];
const Blog = () => {
  return <>
      <div className="vl-blog-v1-area sp1">
        <Container>
          <Row>
            <Col lg={8}>
              <Row>
                {blog.map((item, idx) => <Col lg={12} md={12} key={idx}>
                    <div className="vl-blog-1-item1">
                      <div className="vl-blog-1-thumb image-anime">
                        <Image src={item.image} alt="" />
                      </div>
                      <div className="vl-blog-1-content">
                        <div className="vl-blog-meta">
                          <ul className='p-0 m-0'>
                            <li>
                              <Link href="">
                                <Image src={calender1} alt="" /> 26 August 2024 <span> | </span>
                              </Link>
                            </li>
                            <li>
                              <Link href="">
                                <Image src={user1} alt="" />
                                {item.name}
                              </Link>
                            </li>
                          </ul>
                        </div>
                        <div className="space20" />
                        <h4 className="vl-blog-1-title">
                          <Link href="/blog-single">{item.title}</Link>
                        </h4>
                        <div className="space16" />
                        <p>{item.description}</p>
                        <div className="vl-blog-1-icon">
                          <Link href="/blog-single">
                            <FaArrowRight />
                          </Link>
                        </div>
                      </div>
                    </div>
                  </Col>)}
                <Pagination />
              </Row>
            </Col>
            <Sidebar />
          </Row>
        </Container>
      </div>
    </>;
};
export default Blog;