import element43 from '@/assets/img/elements/elements43.png';
import check6 from '@/assets/img/icons/check6.svg';
import check7 from '@/assets/img/icons/check7.svg';
import logo4 from '@/assets/img/icons/sub-logo4.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const pricings = [{
  image: check6,
  avatar: check7,
  title: 'Tailored IT Solutions'
}, {
  image: check6,
  avatar: check7,
  title: 'Proactive System Monitoring'
}, {
  image: check6,
  avatar: check7,
  title: 'Scalable Cloud Solutions'
}, {
  image: check6,
  avatar: check7,
  title: 'Cybersecurity Protection'
}, {
  image: check6,
  avatar: check7,
  title: 'Secure Data Backup & Recovery'
}, {
  image: check6,
  avatar: check7,
  title: 'Custom Software Development'
}, {
  image: check6,
  avatar: check7,
  title: 'IT Infrastructure Management'
}, {
  image: check6,
  avatar: check7,
  title: 'Data Encryption & Compliance'
}];
const Pricing = () => {
  return <>
      <div className="pricing4-section-area sp2" id="pricing">
        <Container>
          <Row>
            <Col lg={6} className="m-auto">
              <div className="pricing-header text-center heading6 space-margin60">
                <h5>
                  <Image src={logo4} alt="" />
                  Pricing Plan
                </h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">
                  Tailored Pricing Plans For <br className="d-lg-block d-none" /> Your IT Requirements
                </h2>
              </div>
            </Col>
          </Row>
          <div className="space100" />
          <Row>
            <Col lg={4} md={6} data-aos="flip-right" data-aos-duration={800}>
              <div className="pricing-single-boxarea">
                <div className="price">
                  <h2>$60</h2>
                  <div className="space14" />
                  <p>Per Month</p>
                  <Image src={element43} alt="" />
                </div>
                <p>Basic Packages</p>
                <div className="space16" />
                <h3>Basic Plan</h3>
                <ul className="p-0">
                  {pricings.map((item, idx) => <li key={idx}>
                      <Image src={item.image} alt="" />
                      {item.title}
                    </li>)}
                </ul>
                <div className="space32" />
                <div className="btn-area1">
                  <Link href="" className="vl-btn5">
                    <span className="demo">Choose A Plan</span>
                    <span className="arrow">
                      <FaArrowRight className="fa-solid fa-arrow-right" />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
            <Col lg={4} md={6} data-aos="flip-left" data-aos-duration={1000}>
              <div className="space100 d-lg-none d-md-none d-block" />
              <div className="pricing-single-boxarea box2">
                <div className="price">
                  <h2>$80</h2>
                  <div className="space14" />
                  <p>Per Month</p>
                  <Image src={element43} alt="" />
                </div>
                <p>Pro Packages</p>
                <div className="space16" />
                <h3>Pro Plan</h3>
                <ul className="p-0">
                  {pricings.map((item, idx) => <li key={idx}>
                      <Image src={item.avatar} alt="" />
                      {item.title}
                    </li>)}
                </ul>
                <div className="space32" />
                <div className="btn-area1">
                  <Link href="#" className="vl-btn5">
                    <span className="demo">Choose A Plan</span>
                    <span className="arrow">
                      <FaArrowRight className="fa-solid" />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
            <Col lg={4} md={6} data-aos="flip-right" data-aos-duration={1200}>
              <div className="space100 d-lg-none d-md-block" />
              <div className="pricing-single-boxarea">
                <div className="price">
                  <h2>$70</h2>
                  <div className="space14" />
                  <p>Per Month</p>
                  <Image src={element43} alt="" />
                </div>
                <p>Premium Packages</p>
                <div className="space16" />
                <h3>Premium Plan</h3>
                <ul className="p-0">
                  {pricings.map((item, idx) => <li key={idx}>
                      <Image src={item.image} alt="" />
                      {item.title}
                    </li>)}
                </ul>
                <div className="space32" />
                <div className="btn-area1">
                  <Link href="#" className="vl-btn5">
                    <span className="demo">Choose A Plan</span>
                    <span className="arrow">
                      <FaArrowRight className="fa-solid" />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Pricing;