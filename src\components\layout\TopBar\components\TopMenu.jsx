import demo1 from '@/assets/img/all-images/demo/demo-img1.png';
import demo2 from '@/assets/img/all-images/demo/demo-img2.png';
import demo3 from '@/assets/img/all-images/demo/demo-img3.png';
import demo4 from '@/assets/img/all-images/demo/demo-img4.png';
import demo5 from '@/assets/img/all-images/demo/demo-img5.png';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Row } from 'react-bootstrap';
import { FaAngleDown, FaAngleRight, FaArrowRight } from 'react-icons/fa6';
const TopMenu = ({
  classname,
  arrow,
  button
}) => {
  return <>
      <ul className='p-0 m-0'>
        <li className="has-dropdown">
          <Link href="/">
            Home
          </Link>
        </li>
        <li className="has-dropdown">
          <Link href="/about">
            About Us
          </Link>
        </li>
        <li>
          <Link href="/service">
            Services
            <span>
              <FaAngleDown className="fa-solid d-lg-inline d-none m-1" />
            </span>
          </Link>
          <ul className="sub-menu">
            <li>
              <Link href="/service-details">Web Application</Link>
            </li>
            <li>
              <Link href="/service-details">Website Development</Link>
            </li>
            <li>
              <Link href="/service-details">App Development</Link>
            </li>
            <li>
              <Link href="/service-details">Digital Marketing</Link>
            </li>
            <li>
              <Link href="/service-details">Graphic Design</Link>
            </li>
            
          </ul>
        </li>
        <li>
          <Link href="">
            Case Study
          </Link>

        </li>
        <li>
          <Link href="/blog">
            Blogs
          </Link>
        </li>
        <li>
          <Link href="/contact">Contact</Link>
        </li>
      </ul>
    </>;
};
export default TopMenu;