'use client';

import hero3 from '@/assets/img/all-images/bg/hero-bg3.png';
import hero1 from '@/assets/img/all-images/hero/hero-img2.png';
import heroimg3 from '@/assets/img/all-images/hero/hero-img3.png';
import element19 from '@/assets/img/elements/elements19.png';
import element20 from '@/assets/img/elements/elements20.png';
import arrow1 from '@/assets/img/icons/arrow1.svg';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import CountUp from 'react-countup';
import { FaArrowRight } from 'react-icons/fa6';
const Hero = () => {
  return <>
      <div className="hero2-section-area" style={{
      backgroundImage: `url(${hero3.src})`
    }}>
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <div className="hero2-header heading3">
                <h5 data-aos="fade-left" data-aos-duration={800}>
                  <Image src={logo2} alt="" />
                  Create your Dream Project With Us
                </h5>
                <div className="space24" />
                <h1 className="text-anime-style-3">Experience With BPO Call Centre Solutions</h1>
                <div className="space18" />
                <p data-aos="fade-left" data-aos-duration={900}>
                  Outsourcing your call centre operations can revolutionize the way manage customer support, with our expert an team handling every.
                </p>
                <div className="space32" />
                <div className="counter-boxarea">
                  <Row>
                    <Col lg={4} md={4} xs={6} data-aos="zoom-in" data-aos-duration={800}>
                      <div className="counter-box">
                        <h2>
                          <CountUp className="counter" {...{
                          duration: 10
                        }} end={200} start={0} />k
                        </h2>
                        <div className="space16" />
                        <p>Happy Customer</p>
                      </div>
                    </Col>
                    <Col lg={4} md={4} xs={6} data-aos="zoom-in" data-aos-duration={900}>
                      <div className="counter-box">
                        <h2>
                          <CountUp className="counter" {...{
                          duration: 2
                        }} end={20} start={0} />+
                        </h2>
                        <div className="space16" />
                        <p>Years Experience</p>
                      </div>
                    </Col>
                    <Col lg={4} md={4} data-aos="zoom-in" data-aos-duration={1000}>
                      <div className="space30 d-md-none d-block" />
                      <div className="counter-box box2">
                        <h2>
                          <CountUp className="counter" {...{
                          duration: 10
                        }} end={24} start={0} />/
                          <CountUp className="counter" {...{
                          duration: 1
                        }} end={7} start={0} />
                        </h2>
                        <div className="space16" />
                        <p>Customer Support</p>
                      </div>
                    </Col>
                  </Row>
                </div>
                <div className="space32" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1200}>
                  <Link href="/contact" className="vl-btn3">
                    <span className="demo">Boost Your Customer Support</span>
                    <span className="arrow">
                      <FaArrowRight />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
            <Col lg={6}>
              <div className="hero2-images-area">
                <Row>
                  <Col lg={6} md={6}>
                    <div className="images">
                      <div className="img1 ">
                        <Image src={hero1} alt="" />
                      </div>
                      <div className="space30" />
                      <div className="arrow-circle text-end">
                        <Link href="">
                          <Image src={element20} alt="" className="elements20 keyframe5" />
                          <Image src={arrow1} alt="" className="arrow1" />
                        </Link>
                      </div>
                      <Image src={element19} alt="" className="elements19" />
                    </div>
                  </Col>
                  <Col lg={6} md={6}>
                    <div className="img2 ">
                      <Image src={heroimg3} alt="" />
                    </div>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Hero;