import About from './components/About';
import CTA from './components/CTA';
import Faq from './components/Faq';
import Features from './components/Features';
import Footer from './components/Footer';
import Header from './components/Header';
import Hero from './components/Hero';
import Service from './components/Service';
import Solution from './components/Solution';
import Testimonial from './components/Testimonial';
import logo from '@/assets/img/logo/fav-logo4.png';
export const metadata = {
  title: 'Web Circle Technology  - Technology & It Solutions Services',
  icons: {
    icon: logo.src
  }
};
const page = () => {
  return <>
      <Header />
      <Hero />
      <Features />
      <About />
      <Service />
      <div className="space80 d-lg-block d-none" />
      <Solution />
      <Testimonial />
      <Faq />
      <CTA />
      <Footer />
    </>;
};
export default page;