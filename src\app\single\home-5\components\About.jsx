'use client';

import img10 from '@/assets/img/all-images/about/about-img10.png';
import img9 from '@/assets/img/all-images/about/about-img9.png';
import bg3 from '@/assets/img/all-images/bg/about-bg3.png';
import element47 from '@/assets/img/elements/elements47.png';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import CountUp from 'react-countup';
import { FaArrowRight } from 'react-icons/fa6';
const About = () => {
  return <>
      <div className="about5-section-area sp1" id="about">
        <Container>
          <Row className="align-items-center">
            <Col lg={5} data-aos="zoom-in" data-aos-duration={1000}>
              <div className="img1" style={{
              backgroundImage: `url(${bg3.src})`,
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              backgroundSize: 'cover'
            }}>
                <Image src={img9} alt="" className="about-img9" />
                <Image src={img10} alt="" className="about-img10 aniamtion-key-1" />
                <Image src={element47} alt="" className="elements47" />
              </div>
            </Col>
            <Col lg={1} />
            <Col lg={6}>
              <div className="about5-header heading8">
                <h5 data-aos="fade-left" data-aos-duration={800}>
                  About Application Solution
                </h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">Application&nbsp;Solutions A Grow Business Quickly</h2>
                <div className="space16" />
                <p data-aos="fade-left" data-aos-duration={900}>
                  We’re dedicated crafting cutting-edge mobile applications that elevate your business. Our skilled team of developers leverages
                  latest technologies deliver seamless, scalable an secure apps iOS, Android cross-platform environments.
                </p>
                <div className="space32" />
                <div className="counter-boxarea">
                  <Row>
                    <Col lg={4} md={4} xs={6} data-aos="zoom-in" data-aos-duration={800}>
                      <div className="counter-box">
                        <h2>
                          <CountUp start={0} end={98} {...{
                          duration: 10
                        }} className="counter" />%
                        </h2>
                        <div className="space16" />
                        <p>Positive Rate</p>
                      </div>
                    </Col>
                    <Col lg={4} md={4} xs={6} data-aos="zoom-in" data-aos-duration={900}>
                      <div className="counter-box">
                        <h2>
                          <CountUp start={0} end={28} {...{
                          duration: 15
                        }} className="counter" />K
                        </h2>
                        <div className="space16" />
                        <p>Download App</p>
                      </div>
                    </Col>
                    <Col lg={4} md={4} data-aos="zoom-in" data-aos-duration={1000}>
                      <div className="space30 d-md-none d-block" />
                      <div className="counter-box box2">
                        <h2>
                          <CountUp start={0} end={32} {...{
                          duration: 10
                        }} className="counter" />+
                        </h2>
                        <div className="space16" />
                        <p>New Features</p>
                      </div>
                    </Col>
                  </Row>
                </div>
                <div className="space32" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1100}>
                  <Link className="vl-btn6" href="/contact">
                    Start Your Project Now
                    <span className="arrow1">
                      <FaArrowRight className="fa-solid" />
                    </span>
                    <span className="arrow2">
                      <FaArrowRight className="fa-solid" />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default About;