'use client';

import { useEffect } from 'react';
import 'glightbox/dist/css/glightbox.min.css';

const GlightBox = ({
  children
}) => {
  useEffect(() => {
    // Dynamic import to avoid SSR issues
    const initGlightbox = async () => {
      const { default: glightbox } = await import('glightbox');
      const instance = glightbox({
        openEffect: 'fade',
        closeEffect: 'fade'
      });
      return instance;
    };

    let instance;
    initGlightbox().then(inst => {
      instance = inst;
    });

    return () => {
      if (instance) {
        instance.destroy();
      }
    };
  }, []);

  return <>{children}</>;
};
export default GlightBox;