@use '../../utils/' as * ;

/*============= CONTACT CSS AREA ===============*/
.contact-inner-area {
    position: relative;
    z-index: 1;
    .widget-contactbox {
        background: #EFF1FF;
        border-radius: 8px;
        padding: 24px;
        position: relative;
        display: flex;
        align-items: center;
        z-index: 1;
        &:hover {
            &::after {
                width: 100%;
                left: 0;
                transition: all .4s;
                visibility: visible;
                opacity: 1;
            }
            .content {
                h4 {
                    color: var(--ztc-text-text-1);
                    transition: all .4s;
                }
                a {
                    color: var(--ztc-text-text-1);
                    opacity: 80%;
                    transition: all .4s;
                }
            }
        }
        &::after {
            background: var(--ztc-bg-bg-5);
            position: absolute;
            content: "";
            height: 100%;
            left: 50%;
            width: 10px;
            border-radius: 10px;
            top: 0;
            transition: all .4s;
            visibility: hidden;
            opacity: 0;
            z-index: -1;
        }
        .icons {
            height: 60px;
            width: 60px;
            text-align: center;
            line-height: 60px;
            display: inline-block;
            transition: all .4s;
            background: var(--ztc-bg-bg-5);
            border-radius: 50%;
        }
        .content {
            padding-left: 16px;
            h4 {
                color: var(--ztc-text-text-2);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s18);
                font-style: normal;
                font-weight: var(--ztc-weight-semibold);
                line-height: 18px; 
                transition: all .4s;         
            }
            a {
                color: var(--ztc-text-text-3);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                font-style: normal;
                font-weight: var(--ztc-weight-medium);
                line-height: 16px;
                display: inline-block;
                transition: all .4s;
                margin-top: 10px;        
            }
        }
    }

    .contact-author-boxarea {
        position: relative;
        z-index: 1;
        background: #EFF1FF;
        border-radius: 8px;
        padding: 32px;
        @media #{$xs} {
            margin-top: 30px;
        }
        @media #{$md} {
            margin-top: 30px;
        }
        h3 {
            color: var(--ztc-text-text-2);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s24);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 24px; 
            display: inline-block;       
        }
        .input-area {
            input {
                width: 100%;
                color: var(--ztc-text-text-2);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                font-style: normal;
                font-weight: var(--ztc-weight-medium);
                line-height: 16px;
                background: var(--ztc-bg-bg-1);
                border-radius: 4px;
                padding: 18px;
                margin-top: 16px;
                &::placeholder {
                    color: var(--ztc-text-text-2);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 16px;
                    opacity: 0.7;
                }       
            }

            textarea {
                width: 100%;
                color: var(--ztc-text-text-2);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                font-style: normal;
                font-weight: var(--ztc-weight-medium);
                line-height: 16px;
                background: var(--ztc-bg-bg-1);
                border-radius: 4px;
                padding: 18px;
                margin-top: 16px;
                height: 140px;
                &::placeholder {
                    color: var(--ztc-text-text-2);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 16px;
                    opacity: 0.7;
                }       
            }
            button {
                width: 100%;
                text-align: center;
                border: none;
                outline: none;
            }
        }
    }
}
/*============= CONTACT CSS AREA ENDS===============*/