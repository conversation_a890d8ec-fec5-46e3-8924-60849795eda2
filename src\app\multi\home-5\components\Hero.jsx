import hero6 from '@/assets/img/all-images/bg/hero-bg6.png';
import hero7 from '@/assets/img/all-images/hero/hero-img7.png';
import elements44 from '@/assets/img/elements/elements44.png';
import elements45 from '@/assets/img/elements/elements45.png';
import elements46 from '@/assets/img/elements/elements46.png';
import logo1 from '@/assets/img/icons/sub-logo1.svg';
import GlightBox from '@/components/GlightBox';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight, FaPlay } from 'react-icons/fa6';
const Hero = () => {
  return <>
      <div className="hero5-section-area" style={{
      backgroundImage: `url(${hero6.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Image src={elements44} alt="" className="elements44" />
        <Image src={elements45} alt="" className="elements45" />
        <Image src={elements46} alt="" className="elements46 img-fluid" width={700} />
        <Container>
          <GlightBox>
            <Row className="align-items-center">
              <Col lg={6}>
                <div className="hero6-header heading7">
                  <h5 data-aos="fade-left" data-aos-duration={800}>
                    <span>
                      <Image src={logo1} alt="" />
                    </span>
                    Custom Mobile Application Solutions
                  </h5>
                  <div className="space16" />
                  <h1 className="text-anime-style-3">Transform Your Vision In Mobile App Reality</h1>
                  <div className="space24" />
                  <p data-aos="fade-left" data-aos-duration={900}>
                    We specialize in creating, user-friendly mobile applications that drive results. Whether a need feature-rich app for iOS, Android.
                  </p>
                  <div className="space32" />
                  <div className="btn-area1" data-aos="fade-left" data-aos-duration={1100}>
                    <Link className="vl-btn6" href="/contact">
                      Start Your Project Now
                      <span className="arrow1">
                        <FaArrowRight />
                      </span>
                      <span className="arrow2">
                        <FaArrowRight />
                      </span>
                    </Link>
                    <Link href="https://www.youtube.com/watch?v=Y8XpQpW5OVY" className="glightbox play popup-youtube">
                      <span className="icon">
                        <FaPlay className="fa-solid" />
                      </span>
                      <span className="text">Play Video</span>
                    </Link>
                  </div>
                </div>
              </Col>
              <Col lg={1} />
              <Col lg={3}>
                <div className="img1 image-anime">
                  <Image src={hero7} alt="" height={616} />
                </div>
              </Col>
            </Row>
          </GlightBox>
        </Container>
      </div>
    </>;
};
export default Hero;