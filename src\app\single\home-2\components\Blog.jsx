import bg1 from '@/assets/img/all-images/bg/footer-bg2.png';
import blog3 from '@/assets/img/all-images/blog/blog-img3.png';
import blog4 from '@/assets/img/all-images/blog/blog-img4.png';
import blog5 from '@/assets/img/all-images/blog/blog-img5.png';
import calender1 from '@/assets/img/icons/calender1.svg';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const user = [{
  image: blog3,
  date: '4 August 2024',
  description: 'How to Choose the Right BPO Partner for Your Best Business'
}, {
  image: blog4,
  date: '5 August 2024',
  description: 'The Future of BPO: Trends On Shaping A Customer Support'
}, {
  image: blog5,
  date: '6 August 2024',
  description: 'From Data to Decisions: Using Analytic Call Centre Operation'
}];
const Blog = () => {
  return <>
      <div className="vl-blog-2-area sp1" id="blog" style={{
      backgroundImage: `url(${bg1.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Container>
          <Row>
            <Col lg={6} className="m-auto">
              <div className="vl-blog-1-section-box heading4 text-center space-margin60">
                <h5 className="vl-section-subtitle">
                  <span>
                    <Image src={logo2} alt="" />
                  </span>
                  Our Latest Blogs
                </h5>
                <div className="space20" />
                <h2 className="vl-section-title text-anime-style-3">Stay Informed With Our Blog</h2>
              </div>
            </Col>
          </Row>
          <Row>
            {user.map((item, idx) => <Col key={idx} lg={4} md={6} data-aos="fade-left" data-aos-duration={900 + idx * 100}>
                <div className="vl-blog-1-item">
                  <div className="vl-blog-1-thumb image-anime">
                    <Image src={item.image} alt="" />
                  </div>
                  <div className="vl-blog-1-content">
                    <div className="vl-blog-meta">
                      <ul className="p-0 m-0">
                        <li>
                          <Link href="">
                            <Image src={calender1} alt="" /> {item.date}
                          </Link>
                        </li>
                      </ul>
                    </div>
                    <div className="space16" />
                    <h4 className="vl-blog-1-title">
                      <Link href="/blog-single">{item.description}</Link>
                    </h4>
                    <div className="space20" />
                    <Link href="/blog-single" className="readmore">
                      Learn More <FaArrowRight className="fa-solid" />
                    </Link>
                  </div>
                </div>
              </Col>)}
          </Row>
        </Container>
      </div>
    </>;
};
export default Blog;