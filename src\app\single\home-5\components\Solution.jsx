import img11 from '@/assets/img/all-images/about/about-img11.png';
import img12 from '@/assets/img/all-images/about/about-img12.png';
import icon17 from '@/assets/img/icons/service-icon17.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const Solution = () => {
  return <>
      <div className="about6-section-area sp1" id="work">
        <Container>
          <Row>
            <Col lg={5}>
              <div className="about6-header heading8">
                <h5 data-aos="fade-left" data-aos-duration={800}>
                  About Application Solution
                </h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">After&nbsp;Development How We Perform App On Live</h2>
                <div className="space16" />
                <p data-aos="fade-left" data-aos-duration={900}>
                  With our expertise, you can confidently step into the mobile world an stay ahead competition with precision, innovation.
                </p>
                <div className="space24" />
                <div className="about-boxarea" data-aos="fade-left" data-aos-duration={1000}>
                  <div className="icons">
                    <Image src={icon17} alt="" />
                  </div>
                  <div className="content-area">
                    <Link href="">Auto Update Features</Link>
                    <div className="space16" />
                    <p>We focus on creating apps that only meet your immediate an needs but are scalable adaptable</p>
                  </div>
                </div>
                <div className="space24" />
                <div className="about-boxarea" data-aos="fade-left" data-aos-duration={1100}>
                  <div className="icons">
                    <Image src={icon17} alt="" />
                  </div>
                  <div className="content-area">
                    <Link href="">Enable Google Analytics</Link>
                    <div className="space16" />
                    <p>We focus on creating apps that only meet your immediate an needs but are scalable adaptable</p>
                  </div>
                </div>
                <div className="space32" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1200}>
                  <Link className="vl-btn6" href="">
                    Start Your Project Now
                    <span className="arrow1">
                      <FaArrowRight />
                    </span>
                    <span className="arrow2">
                      <FaArrowRight />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
            <Col lg={2} />
            <Col lg={5}>
              <div className="images">
                <div className="img1 image-anime">
                  <Image src={img11} alt="" />
                </div>
                <Image src={img12} alt="" className="about-img12 aniamtion-key-1" />
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Solution;