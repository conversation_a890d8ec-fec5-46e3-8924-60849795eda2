import faq1 from '@/assets/img/all-images/faq/faq-img1.png';
import element27 from '@/assets/img/elements/elements27.png';
import sublogo3 from '@/assets/img/icons/sub-logo3.svg';
import Image from 'next/image';
import { Accordion, <PERSON><PERSON>rdionB<PERSON>, AccordionHeader, AccordionItem, Col, Container, Row } from 'react-bootstrap';
const faqs = [{
  question: 'How do I contact the Help Desk?',
  answer: 'Yes, you can request a callback through our Support Portal or by sending an email contact information and preferred time for a call. We’ll do our best to accommodate request.'
}, {
  question: 'Can I request a callback from Help Desk?',
  answer: 'Yes, you can request a callback through our Support Portal or by sending an email contact information and preferred time for a call. We’ll do our best to accommodate request.'
}, {
  question: 'How long will it take to get a response?',
  answer: 'Yes, you can request a callback through our Support Portal or by sending an email contact information and preferred time for a call. We’ll do our best to accommodate request.'
}, {
  question: 'What the Help Desk’s hours of operation?',
  answer: 'Yes, you can request a callback through our Support Portal or by sending an email contact information and preferred time for a call. We’ll do our best to accommodate request.'
}, {
  question: 'Can I track the status of support request?',
  answer: 'Yes, you can request a callback through our Support Portal or by sending an email contact information and preferred time for a call. We’ll do our best to accommodate request.'
}];
const Faq = () => {
  return <>
      <div className="faq3-section-area sp1">
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <div className="faq-header heading5">
                <h5>
                  <Image src={sublogo3} alt="" />
                  All faq question
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">Frequently Asked Question</h2>
              </div>
              <div className="space50" />
              <div className="images">
                <div className="img1 image-anime ">
                  <Image src={faq1} alt="" width={636} className='img-fluid' />
                </div>
                <div className="space30 d-lg-none d-block" />
                <Image src={element27} alt="" className="elements27" />
              </div>
            </Col>
            <Col lg={1} />
            <Col lg={5}>
              <div className="faq-accordion-area">
                <Accordion defaultActiveKey={'1'} className="accordion-flush active" id="accordionExample">
                  {faqs.map((item, idx) => <AccordionItem eventKey={`${idx + 1}`} key={idx} style={{
                  marginBottom: '20px'
                }}>
                      <AccordionHeader as={'h2'}>{item.question}</AccordionHeader>
                      <AccordionBody>
                        <p>{item.answer}</p>
                      </AccordionBody>
                    </AccordionItem>)}
                  <div className="space24" />
                </Accordion>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Faq;