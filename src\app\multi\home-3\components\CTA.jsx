import element26 from '@/assets/img/elements/elements26.png';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
const CTA = () => {
  return <>
      <div className="cta3-section-area">
        <Container>
          <Row>
            <Col lg={12}>
              <div className="cta-bg-area">
                <Image src={element26} alt="" className="elements26" />
                <Row>
                  <Col lg={8} className="m-auto">
                    <div className="cta-header heading1 text-center">
                      <h2 data-aos="fade-left" data-aos-duration={700}>
                        We’re Ready To Help You, Anytime
                      </h2>
                      <div className="space16" />
                      <p data-aos="fade-left" data-aos-duration={800}>
                        Our dedicated support team is available to resolve any issues or answer your questions quickly. No matter what problem you're
                        facing, we're here ensure you get the help you need without.
                      </p>
                      <div className="space24" />
                      <div className="btn-area1">
                        <Link href="/contact" className="vl-btn4">
                          Reach out to our Help Desk today!
                        </Link>
                        <Link href="/contact" className="vl-btn4 bnt2">
                          Request a support now
                        </Link>
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default CTA;