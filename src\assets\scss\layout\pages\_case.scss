@use '../../utils/' as *;

/*============= CASE STUDY CSS AREA ENDS ===============*/
.case1-section-area {
  position: relative;
  z-index: 1;
  background: #eff1ff;
  .slick-list {
    overflow: visible;
  }
  .slick-slide {
    padding-right: 30px;
    box-sizing: border-box;
  }
  .case-works-section-area {
    position: relative;
    z-index: 1;
    
    .others-widget-area {
      background: var(--ztc-bg-bg-1);
      border-radius: 16px;
      padding: 50px;
      position: relative;
      top: -100px;
      margin-top: -280px;
      @media #{$md} {
        margin-top: -230px;
      }
      @media #{$xs} {
        margin-top: -230px;
        padding: 30px;
      }
      .card-boxarea {
        &:hover {
          h3 {
            transform: rotateY(360deg);
            transition: all 0.4s;
          }
        }
        h3 {
          color: var(--ztc-text-text-1);
          text-align: center;
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s24);
          font-style: normal;
          font-weight: var(--ztc-weight-bold);
          line-height: 60px;
          letter-spacing: -0.24px;
          display: inline-block;
          transition: all 0.4s;
          border-radius: 50%;
          height: 60px;
          width: 60px;
          background: var(--Main-Color, linear-gradient(90deg, #2e0797 0%, #726efc 100%));
        }
        .content-area {
          background: #f2f4ff;
          padding: 28px;
          border-radius: 4px;
          text-align: center;
          margin: 0 40px 0 0;
          position: relative;
          z-index: 1;
          @media #{$xs} {
            margin: 0;
          }
          &:hover {
            &::after {
              width: 100%;
              left: 0;
              top: 0;
              transition: all 0.4s;
              visibility: visible;
              opacity: 1;
            }
            a {
              color: var(--ztc-text-text-1);
              transition: all 0.4s;
            }
            p {
              color: var(--ztc-text-text-1);
              transition: all 0.4s;
              opacity: 80%;
            }
          }
          &::after {
            position: absolute;
            content: '';
            height: 100%;
            width: 10px;
            left: 50%;
            transition: all 0.4s;
            top: 0;
            background: var(--ztc-bg-bg-5);
            z-index: -1;
            border-radius: 8px;
            visibility: hidden;
            opacity: 0;
          }
          a {
            color: var(--ztc-text-text-2);
            text-align: center;
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s20);
            font-style: normal;
            font-weight: var(--ztc-weight-bold);
            line-height: 20px;
            display: inline-block;
            transition: all 0.4s;
          }
          p {
            color: var(--ztc-text-text-2);
            text-align: center;
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 26px; /* 162.5% */
            letter-spacing: 0.16px;
            opacity: 0.8;
            transition: all 0.4s;
          }
        }

        .content-area2 {
          background: #f2f4ff;
          padding: 28px;
          border-radius: 4px;
          text-align: center;
          margin: 0 0 0 40px;
          position: relative;
          z-index: 1;
          @media #{$xs} {
            margin: 0;
          }
          &:hover {
            &::after {
              width: 100%;
              left: 0;
              top: 0;
              transition: all 0.4s;
              visibility: visible;
              opacity: 1;
            }
            a {
              color: var(--ztc-text-text-1);
              transition: all 0.4s;
            }
            p {
              color: var(--ztc-text-text-1);
              transition: all 0.4s;
              opacity: 80%;
            }
          }
          &::after {
            position: absolute;
            content: '';
            height: 100%;
            width: 10px;
            left: 50%;
            transition: all 0.4s;
            top: 0;
            background: var(--ztc-bg-bg-5);
            z-index: -1;
            border-radius: 8px;
            visibility: hidden;
            opacity: 0;
          }
          a {
            color: var(--ztc-text-text-2);
            text-align: center;
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s20);
            font-style: normal;
            font-weight: var(--ztc-weight-bold);
            line-height: 20px;
            display: inline-block;
            transition: all 0.4s;
          }
          p {
            color: var(--ztc-text-text-2);
            text-align: center;
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 26px; /* 162.5% */
            letter-spacing: 0.16px;
            opacity: 0.8;
            transition: all 0.4s;
          }
        }
        &.box1 {
          h3 {
            position: relative;
            left: -75px;
            margin-bottom: 20px;
            @media #{$xs} {
              left: 0;
            }
          }
        }
        &.box2 {
          h3 {
            position: relative;
            left: -73px;
            margin-top: 13px;
            @media #{$xs} {
              left: 0;
            }
          }
        }

        &.box3 {
          h3 {
            position: relative;
            left: 74px;
            margin-top: -33px;
            margin-bottom: 16px;
            @media #{$xs} {
              left: 0;
            }
          }
        }
        &.box4 {
          h3 {
            position: relative;
            left: 74px;
            margin-top: 16px;
            margin-bottom: -16px;
            @media #{$xs} {
              left: 0;
            }
          }
        }
      }
      .images {
        position: relative;
        z-index: 1;
        @media #{$xs} {
          margin-top: 30px;
          margin-bottom: 30px;
        }

        .elements12 {
          position: absolute;
          top: -10px;
          left: -100px;
          @media #{$xs} {
            display: none;
          }
          @media #{$md} {
            display: none;
          }
        }

        .elements13 {
          position: absolute;
          bottom: -10px;
          left: -100px;
          @media #{$xs} {
            display: none;
          }
          @media #{$md} {
            display: none;
          }
        }

        .elements14 {
          position: absolute;
          top: -10px;
          right: -100px;
          @media #{$xs} {
            display: none;
          }
          @media #{$md} {
            display: none;
          }
        }
        .elements15 {
          position: absolute;
          bottom: -10px;
          right: -100px;
          @media #{$xs} {
            display: none;
          }
          @media #{$md} {
            display: none;
          }
        }
        .img1 {
          position: relative;
          z-index: 1;
          background: #f1f0fe;
          padding: 16px;
          border-radius: 50%;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }
        }
      }
    }
  }
  .case-header {
    @media #{$xs} {
      text-align: center;
    }
    h5 {
      &::after {
        border-radius: 8px;
        background: linear-gradient(0deg, rgba(114, 110, 252, 0.1) 0%, rgba(114, 110, 252, 0.1) 100%);
        backdrop-filter: blur(5px);
      }
    }
  }
  .case-slider-widget {
    position: relative;
    z-index: 1;
    .owl-stage-outer {
      overflow: inherit !important;
    }
    .owl-nav {
      position: absolute;
      top: -110px;
      right: 0;
      @media #{$xs} {
        position: relative;
        z-index: 1;
        top: 0;
        left: 0;
        text-align: center;
        margin-top: 30px;
      }
      @media #{$md} {
        position: relative;
        z-index: 1;
        top: 0;
        left: 0;
        text-align: center;
        margin-top: 30px;
      }
      button {
        height: 60px;
        width: 60px;
        text-align: center;
        line-height: 60px;
        display: inline-block;
        transition: all 0.4s;
        border-radius: 50%;
        background: var(--ztc-bg-bg-1);
        border: none;
        outline: none;
        font-size: var(--ztc-font-size-font-s24);
        color: #2e0797;
        &.owl-prev {
          margin: 0 16px 0 0;
        }
        &:hover {
          background: #2e0797;
          color: var(--ztc-text-text-1);
          transition: all 0.4s;
        }
      }
    }
    .case-slider-boxarea {
      position: relative;
      z-index: 1;
      &:hover {
        .img1 {
          img {
            transform: scale(1.1) rotate(-4deg);
            transition: all 0.4s;
          }
        }
        .content-area {
          &::after {
            visibility: visible;
            opacity: 1;
            transition: all 0.4s;
            width: 100%;
            left: 0;
            top: 0;
          }
          a {
            color: var(--ztc-text-text-1);
            transition: all 0.4s;
          }
          p {
            color: var(--ztc-text-text-1);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.4s;
          }
          .arrow {
            a {
              transform: rotate(0);
              transition: all 0.4s;
            }
          }
        }
      }
      .img1 {
        overflow: hidden;
        transition: all 0.4s;
        border-radius: 16px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 16px;
          transition: all 0.4s;
        }
      }
      .content-area {
        background: var(--ztc-bg-bg-1);
        border-radius: 8px;
        padding: 20px 40px 20px 24px;
        position: relative;
        z-index: 2;
        display: inline-block;
        margin-top: -110px;
        margin-left: 28px;
        @media #{$xs} {
          padding: 20px;
          margin-left: 16px;
        }
        @media #{$md} {
          padding: 20px;
          margin-left: 16px;
        }
        &::after {
          position: absolute;
          content: '';
          height: 100%;
          width: 10px;
          left: 50%;
          transition: all 0.4s;
          top: 0;
          background: var(--ztc-bg-bg-5);
          z-index: -1;
          border-radius: 8px;
          visibility: hidden;
          opacity: 0;
        }
        p {
          color: var(--Main-Color, #6f69f7);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-semibold);
          line-height: 16px;
          display: inline-block;
          transition: all 0.4s;
          border-radius: 6px;
          background: rgba(111, 105, 247, 0.1);
          padding: 8px 10px;
        }
        a {
          color: var(--ztc-text-text-2);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s20);
          font-style: normal;
          font-weight: var(--ztc-weight-semibold);
          line-height: 20px;
          display: inline-block;
          transition: all 0.4s;
        }
        .arrow {
          position: absolute;
          top: -15px;
          right: -15px;
          a {
            height: 50px;
            width: 50px;
            text-align: center;
            line-height: 50px;
            transition: all 0.4s;
            display: inline-block;
            border-radius: 50%;
            background: var(--ztc-bg-bg-5);
            color: var(--ztc-text-text-1);
            transform: rotate(-45deg);
          }
        }
      }
    }
  }
}

// Homepage 04 //
.case4-section-area {
  position: relative;
  z-index: 1;
  .elements41 {
    position: absolute;
    top: 0;
    right: 0;
  }
  .slick-slide {
    // padding-left: 15px;
    box-sizing: border-box;
    padding-right: 30px;
  }
  .case4-slider {
    position: relative;
    z-index: 1;
    .owl-nav {
      position: absolute;
      top: -130px;
      right: 0;
      @media #{$xs} {
        position: relative;
        z-index: 1;
        top: 0;
        left: 0;
        text-align: center;
        margin-top: 30px;
      }
      @media #{$md} {
        position: relative;
        z-index: 1;
        top: 0;
        left: 0;
        text-align: center;
        margin-top: 30px;
      }
      button {
        height: 60px;
        width: 60px;
        text-align: center;
        line-height: 60px;
        border-radius: 50%;
        color: var(--ztc-text-text-12);
        transition: all 0.4s;
        display: inline-block;
        background: var(--ztc-bg-bg-1);
        font-size: var(--ztc-font-size-font-s20);
        border: transparent;
        &:hover {
          background: var(--ztc-bg-bg-10);
          color: var(--ztc-text-text-1);
          transition: all 0.4s;
        }
        &.owl-prev {
          margin: 0 16px 0 0;
        }
      }
    }
    .case-single-boxarea {
      position: relative;
      z-index: 1;
      border-radius: 8px;
      overflow: hidden;
      .img1 {
        img {
          height: 100%;
          width: 100%;
          object-fit: cover;
          border-radius: 8px;
        }
      }
      .content-area {
        background: var(--ztc-bg-bg-1);
        border-radius: 8px;
        padding: 24px 60px 24px 24px;
        position: absolute;
        bottom: 20px;
        z-index: 2;
        left: 20px;
        right: 20px;
        a {
          span {
            color: var(--ztc-text-text-10);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 16px;
            display: inline-block;
            transition: all 0.4s;
          }
          color: var(--ztc-text-text-10);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s20);
          font-style: normal;
          font-weight: var(--ztc-weight-semibold);
          line-height: 28px;
          display: inline-block;
          transition: all 0.4s;
          &:hover {
            color: var(--ztc-text-text-12);
            transition: all 0.4s;
          }
        }

        .arrow {
          position: absolute;
          top: -15px;
          right: -15px;
          a {
            height: 50px;
            width: 50px;
            text-align: center;
            line-height: 50px;
            display: inline-block;
            transition: all 0.4s;
            background: linear-gradient(90deg, #202cd3 1.1%, #0778f9 100%);
            color: var(--ztc-text-text-1);
            border-radius: 50%;
            transform: rotate(-45deg);
          }
        }
      }
    }
  }
}

// Inner Pages //
.case-inner-area {
  position: relative;
  z-index: 1;
  .case-slider-boxarea {
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
    &:hover {
      .img1 {
        img {
          transform: scale(1.1) rotate(-4deg);
          transition: all 0.4s;
        }
      }
      .content-area {
        &::after {
          visibility: visible;
          opacity: 1;
          transition: all 0.4s;
          width: 100%;
          left: 0;
          top: 0;
        }
        a {
          color: var(--ztc-text-text-1);
          transition: all 0.4s;
        }
        p {
          color: var(--ztc-text-text-1);
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.1);
          transition: all 0.4s;
        }
        .arrow {
          a {
            transform: rotate(0);
            transition: all 0.4s;
          }
        }
      }
    }
    .img1 {
      overflow: hidden;
      transition: all 0.4s;
      border-radius: 16px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 16px;
        transition: all 0.4s;
      }
    }
    .content-area {
      background: var(--ztc-bg-bg-1);
      border-radius: 8px;
      padding: 20px 40px 20px 24px;
      position: relative;
      z-index: 2;
      display: inline-block;
      margin-top: -110px;
      margin-left: 28px;
      box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
      @media #{$xs} {
        padding: 20px;
        margin-left: 0;
      }
      @media #{$md} {
        padding: 20px;
        margin-left: 0;
      }
      &::after {
        position: absolute;
        content: '';
        height: 100%;
        width: 10px;
        left: 50%;
        transition: all 0.4s;
        top: 0;
        background: var(--ztc-bg-bg-5);
        z-index: -1;
        border-radius: 8px;
        visibility: hidden;
        opacity: 0;
      }
      p {
        color: var(--Main-Color, #6f69f7);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        font-style: normal;
        font-weight: var(--ztc-weight-semibold);
        line-height: 16px;
        display: inline-block;
        transition: all 0.4s;
        border-radius: 6px;
        background: rgba(111, 105, 247, 0.1);
        padding: 8px 10px;
      }
      a {
        color: var(--ztc-text-text-2);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s20);
        font-style: normal;
        font-weight: var(--ztc-weight-semibold);
        line-height: 20px;
        display: inline-block;
        transition: all 0.4s;
      }
      .arrow {
        position: absolute;
        top: -15px;
        right: -15px;
        a {
          height: 50px;
          width: 50px;
          text-align: center;
          line-height: 50px;
          transition: all 0.4s;
          display: inline-block;
          border-radius: 50%;
          background: var(--ztc-bg-bg-5);
          color: var(--ztc-text-text-1);
          transform: rotate(-45deg);
        }
      }
    }
  }
}

.case-single-section-area {
  position: relative;
  z-index: 1;
  .case-widgets-area {
    .search-area {
      position: relative;
      z-index: 1;
      border-radius: 8px;
      background: var(--Gray-Color, #eff1ff);
      padding: 24px 28px;
      form {
        position: relative;
        z-index: 1;
        input {
          width: 100%;
          border-radius: 8px;
          background: var(--ztc-bg-bg-1);
          color: var(--Text-Color, #050734);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 16px;
          padding: 19px 16px;
        }
        button {
          border: none;
          background: none;
          outline: none;
          position: absolute;
          right: 12px;
          top: 12px;
          font-size: var(--ztc-font-size-font-s22);
        }
      }
    }

    .tags-area {
      border-radius: 8px;
      background: var(--Gray-Color, #eff1ff);
      padding: 24px 28px;
      ul {
        li {
          display: inline-block;
          a {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            line-height: 18px;
            display: inline-block;
            border-radius: 4px;
            background: var(--ztc-bg-bg-1);
            padding: 10px;
            font-weight: var(--ztc-weight-semibold);
            transition: all 0.4s;
            color: var(--ztc-text-text-2);
            margin-top: 16px;
            position: relative;
            z-index: 1;
            margin-right: 12px;
            @media #{$xs} {
              margin-right: 0;
            }
            &:hover {
              transition: all 0.4s;
              color: var(--ztc-text-text-1);
              &::after {
                visibility: visible;
                opacity: 1;
                transition: all 0.4s;
              }
            }
            &::after {
              position: absolute;
              content: '';
              width: 100%;
              height: 100%;
              left: 0;
              top: 0;
              background: var(--ztc-bg-bg-5);
              transition: all 0.4s;
              border-radius: 8px;
              z-index: -1;
              visibility: hidden;
              opacity: 0;
            }
          }
        }
      }
    }
    .contact-boxarea {
      border-radius: 8px;
      background: var(--Gray-Color, #eff1ff);
      padding: 24px 28px;
      .input-area {
        margin-top: 16px;
        input {
          width: 100%;
          color: var(--ztc-text-text-2);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 16px;
          padding: 20px;
          border-radius: 4px;
          background: var(--ztc-bg-bg-1);
          &::placeholder {
            color: var(--ztc-text-text-3);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 16px;
          }
        }

        textarea {
          width: 100%;
          color: var(--ztc-text-text-2);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 16px;
          padding: 20px;
          border-radius: 4px;
          background: var(--ztc-bg-bg-1);
          height: 120px;
          &::placeholder {
            color: var(--ztc-text-text-3);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 16px;
          }
        }
        button {
          border: none;
          outline: none;
        }
      }
    }
  }
  .case-sider-widget-area.rightside {
    padding: 0 50px 0 0;
    @media #{$xs} {
      padding: 0;
      margin-bottom: 50px;
    }
    @media #{$md} {
      padding: 0;
      margin-bottom: 50px;
    }
  }
  .case-sider-widget-area {
    padding: 0 0 0 50px;
    @media #{$xs} {
      padding: 0;
      margin-top: 50px;
    }
    @media #{$md} {
      padding: 0;
      margin-top: 50px;
    }
    h3 {
      color: var(--ztc-text-text-2);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s32);
      font-style: normal;
      font-weight: var(--ztc-weight-semibold);
      line-height: 32px;
      @media #{$xs} {
        line-height: 40px;
      }
    }
    p {
      color: var(--ztc-text-text-3);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s18);
      font-style: normal;
      font-weight: var(--ztc-weight-medium);
      line-height: 26px;
      letter-spacing: -0.18px;
    }
    .img1 {
      img {
        border-radius: 8px;
      }
    }
    .list {
      li {
        margin-top: 20px;
        background: #eff1ff;
        padding: 14px;
        border-radius: 8px;
        img {
          height: 28px;
          width: 28px;
          object-fit: cover;
          border-radius: 50%;
          margin: 0 4px 0 0;
        }
        color: var(--ztc-text-text-2);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-style: normal;
        font-weight: var(--ztc-weight-semibold);
        line-height: 18px;
      }
    }

    .bg-progress {
      border-radius: 8px;
      background: #f2f4ff;
      padding: 24px;
      .progress-bar {
        margin-bottom: 24px;
      }

      label {
        color: var(--ztc-text-text-2);
        font-family: var(--ztc-family-font1);
        font-size: variable-exists($name: --ztc-font-size-font-s18);
        font-style: normal;
        font-weight: var(--ztc-weight-semibold);
        line-height: 18px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 14px;
      }

      .progress {
        background-color: #dadceb;
        border-radius: 20px;
        height: 10px;
        position: relative;
      }

      .progress-inner {
        border-radius: 40px;
        background: var(--ztc-bg-bg-5);
        height: 100%;
        transition: width 0.4s ease;
      }
    }
  }
}
/*============= CASE STUDY CSS AREA ENDS ===============*/
