import hero5 from '@/assets/img/all-images/bg/hero-bg5.png';
import hero6 from '@/assets/img/all-images/hero/hero-img6.png';
import element31 from '@/assets/img/elements/elements31.png';
import element32 from '@/assets/img/elements/elements32.png';
import element33 from '@/assets/img/elements/elements33.png';
import element34 from '@/assets/img/elements/elements34.png';
import element35 from '@/assets/img/elements/elements35.png';
import element36 from '@/assets/img/elements/elements36.png';
import element37 from '@/assets/img/elements/elements37.png';
import element38 from '@/assets/img/elements/elements38.png';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const Hero = () => {
  return <>
      <div className="hero4-section-area" style={{
      backgroundImage: `url(${hero5.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <div className="hand-img">
          <Image src={element32} alt="" className="elements32" />
          <Image src={element31} alt="" className="elements31 keyframe5" />
        </div>
        <Image src={element33} alt="" className="elements33 aniamtion-key-1" />
        <Image src={element34} alt="" className="elements34" />
        <Image src={element35} alt="" className="elements35" />
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <div className="hero4-heading heading3">
                <h5 data-aos="fade-left" data-aos-duration={800} style={{
                textTransform: 'uppercase'
              }}>
                  <Image src={logo2} alt="" />
                  Innovative Solutions for A Digital World
                </h5>
                <div className="space18" />
                <h1 className="text-anime-style-3">Best Optimized IT For Performance, Security Sustainable Growth</h1>
                <div className="space16" />
                <p data-aos="fade-left" data-aos-duration={1000}>
                  We offer full suite of IT solutions designed meet the needs of modern businesses. Our Web Circle Technology  cybersecurity services to provide
                  robust protection for your data &amp; systems while.
                </p>
                <div className="space32" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1200}>
                  <Link href="/" className="vl-btn5">
                    <span className="demo">Book A Free Consultation</span>
                    <span className="arrow">
                      <FaArrowRight />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
            <Col lg={1} />
            <Col lg={5}>
              <div className="her4-images-area">
                <div className="img1" data-aos="zoom-in" data-aos-duration={1000}>
                  <Image src={hero6} alt="" />
                </div>
                <div className="bg">
                  <Image src={element36} alt="" className="keyframe5" />
                </div>
                <Image src={element37} alt="" className="elements37 aniamtion-key-1" />
                <Image src={element38} alt="" className="elements38 aniamtion-key-2" />
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Hero;