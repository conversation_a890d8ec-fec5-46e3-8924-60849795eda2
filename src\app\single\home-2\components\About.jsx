'use client';

import about4 from '@/assets/img/all-images/about/about-img4.png';
import element23 from '@/assets/img/elements/elements23.png';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import { Col, Container, Row } from 'react-bootstrap';
import { FaCheck } from 'react-icons/fa6';
const About = () => {
  return <>
      <div className="about2-section-area sp1" id="about">
        <Container>
          <Row>
            <Col lg={5} className="m-auto">
              <div className="about2-header text-center heading4 space-margin60">
                <h5>
                  <Image src={logo2} alt="" />
                  Why Choose Us
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">Customer Service That Scales Your Business</h2>
              </div>
            </Col>
          </Row>
          <Row className="align-items-center">
            <Col lg={4} md={6} className="d-lg-block d-none">
              <div className="about-list-box box1">
                <span>
                  <FaCheck />
                </span>
                Significant Cost Savings
              </div>
              <div className="space56" />
              <div className="about-list-box box2">
                <span>
                  <FaCheck />
                </span>
                24/7 Customer Support
              </div>
              <div className="space56" />
              <div className="about-list-box box3">
                <span>
                  <FaCheck />
                </span>
                Onboarding A Integration
              </div>
            </Col>
            <Col lg={4}>
              <div className="about-images">
                <div className="img1 ">
                  <Image src={about4} alt="" />
                </div>
                <Image src={element23} alt="" className="elements10" />
              </div>
              <div className="space30 d-lg-none d-block" />
            </Col>
            <Col lg={4} md={6} className="d-lg-none d-block">
              <div className="about-list-box box1">
                <span>
                  <FaCheck />
                </span>
                Significant Cost Savings
              </div>
              <div className="space56" />
              <div className="about-list-box box2">
                <span>
                  <FaCheck />
                </span>
                24/7 Customer Support
              </div>
              <div className="space56" />
              <div className="about-list-box box3">
                <span>
                  <FaCheck />
                </span>
                Onboarding A Integration
              </div>
              <div className="space56 d-lg-none d-block" />
            </Col>
            <Col lg={4} md={6}>
              <div className="about-list-box2 box1">
                <span>
                  <FaCheck />
                </span>
                Enhance Brand Reputation
              </div>
              <div className="space56" />
              <div className="about-list-box2 box2">
                <span>
                  <FaCheck />
                </span>
                Flexible Contract Terms
              </div>
              <div className="space56" />
              <div className="about-list-box2 box3">
                <span>
                  <FaCheck />
                </span>
                Omni-Channel Support
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default About;