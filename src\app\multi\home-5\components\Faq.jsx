import { Accordion, AccordionBody, Accordion<PERSON><PERSON>er, Accordion<PERSON><PERSON>, Col, Container, <PERSON> } from 'react-bootstrap';
const faqs = [{
  question: 'How long does it take to develop a mobile app?',
  answer: 'The timeline for app development depends on the complexity of the project, the features'
}, {
  question: 'How much does it cost to develop a mobile app?',
  answer: 'The timeline for app development depends on the complexity of the project, the features'
}, {
  question: 'Do offer post-launch support and maintenance?',
  answer: 'The timeline for app development depends on the complexity of the project, the features'
}, {
  question: 'Will my app work on both iOS and Android?',
  answer: 'The timeline for app development depends on the complexity of the project, the features'
}, {
  question: 'How do you ensure the security of the app?',
  answer: 'The timeline for app development depends on the complexity of the project, the features'
}];
const Faq = () => {
  return <>
      <div className="faq5-section-area sp1">
        <Container>
          <Row>
            <Col lg={6} className="m-auto">
              <div className="faq-header text-center heading8 space-margin60">
                <h5>FAQ Question</h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">Frequently Asked Question</h2>
              </div>
            </Col>
          </Row>
          <Row>
            <Col lg={10} className="m-auto">
              <div className="faq-widget-area">
                <Accordion defaultActiveKey={'1'} id="accordionExample">
                  {faqs.map((item, idx) => <AccordionItem style={{
                  marginBottom: '32px'
                }} eventKey={`${idx + 1}`} key={idx} data-aos="fade-up" data-aos-duration={800 + idx * 100}>
                      <AccordionHeader as={'h2'}>{item.question}</AccordionHeader>
                      <div id="collapseOne" className="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                        <AccordionBody>
                          <p>
                            {item.answer}
                            <br className="d-lg-block d-none" /> required, and the platforms you want target (iOS, Android, or both). On average,
                            simple app.
                          </p>
                        </AccordionBody>
                      </div>
                    </AccordionItem>)}
                  <div className="space32" />
                </Accordion>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Faq;