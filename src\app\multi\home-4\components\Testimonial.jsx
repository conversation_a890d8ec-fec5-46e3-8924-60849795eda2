'use client';

import service from '@/assets/img/all-images/bg/service-bg1.png';
import img7 from '@/assets/img/all-images/testimonial/testimonial-img7.png';
import img8 from '@/assets/img/all-images/testimonial/testimonial-img8.png';
import element40 from '@/assets/img/elements/elements40.png';
import logo4 from '@/assets/img/icons/sub-logo4.svg';
import Image from 'next/image';
import Link from 'next/link';
import { useRef } from 'react';
import { Col, Container, Row } from 'react-bootstrap';
import { FaAngleLeft, FaAngleRight, FaStar } from 'react-icons/fa6';
import Slider from 'react-slick';
const testimonials = [{
  image: img7,
  name: '<PERSON>',
  role: 'Shop Store Owner',
  description: 'Working with has been a game- Best changer for our business. Their IT and support team is always responsive, an their expertise has helped us stream our operations, We no longer worry.'
}, {
  image: img8,
  name: '<PERSON>',
  role: 'Shop Store Owner',
  description: 'Our network security has never been stronger. Their proactive monitoring and customized security solutions on have given us peace of mind. Our data is safe, and our compliance has Tech.'
}, {
  image: img7,
  name: '<PERSON> <PERSON>',
  role: 'Shop Store Owner',
  description: 'Has provided us on with top-notch IT services. Their solutions are tailored to our business, &amp; their ongoing support is second to none. We’ve seen significant improvements in our.'
}, {
  image: img7,
  name: 'Henry Gayle',
  role: 'Shop Store Owner',
  description: 'Working with has been a game- Best changer for our business. Their IT and support team is always responsive, an their expertise has helped us stream our operations, We no longer worry.'
}, {
  image: img8,
  name: 'Alex Robertson',
  role: 'Shop Store Owner',
  description: 'Our network security has never been stronger. Their proactive monitoring and customized security solutions on have given us peace of mind. Our data is safe, and our compliance has Tech.'
}, {
  image: img7,
  name: 'Sheldon Jackson',
  role: 'Shop Store Owner',
  description: 'Has provided us on with top-notch IT services. Their solutions are tailored to our business, &amp; their ongoing support is second to none. We’ve seen significant improvements in our.'
}];
const Testimonial = () => {
  const sliderRef = useRef(null);
  const settings = {
    dots: false,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 2000,
    arrows: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    responsive: [{
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }]
  };
  return <>
      <div className="testimonial4-section-area sp1" style={{
      backgroundImage: `url(${service.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Container>
          <Row>
            <Col lg={5}>
              <div className="testimonial-heading heading6 space-margin60">
                <h5>
                  <Image src={logo4} alt="" />
                  Testimonials
                </h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">What Our Clients Say</h2>
              </div>
            </Col>
          </Row>
          <Row>
            <Col lg={12}>
              <div className="testimonial4-slider owl-carousel">
                <Slider {...settings} ref={sliderRef}>
                  {testimonials.map((item, idx) => <div key={idx} className="testimonial-review-box">
                      <div className="man-text">
                        <div className="man">
                          <Image src={item.image} alt="" />
                        </div>
                        <div className="text">
                          <Link href="/team">{item.name}</Link>
                          <div className="space12" />
                          <p>{item.role}</p>
                        </div>
                      </div>
                      <div className="space24" />
                      <p>“{item.description}”</p>
                      <div className="space24" />
                      <div className="logo-area">
                        <Image src={element40} alt="" />
                        <ul className="d-inline-flex gap-1 m-0">
                          <li>
                            <FaStar className="fa-solid" />
                          </li>
                          <li>
                            <FaStar className="fa-solid" />
                          </li>
                          <li>
                            <FaStar className="fa-solid" />
                          </li>
                          <li>
                            <FaStar className="fa-solid" />
                          </li>
                          <li>
                            <FaStar className="fa-solid" />
                          </li>
                        </ul>
                      </div>
                    </div>)}
                </Slider>
                <div className="owl-nav">
                  <button className="owl-prev" onClick={() => sliderRef.current?.slickPrev()}>
                    <FaAngleLeft />
                  </button>
                  <button className="owl-next" onClick={() => sliderRef.current?.slickNext()}>
                    <FaAngleRight />
                  </button>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Testimonial;