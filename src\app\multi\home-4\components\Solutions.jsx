import case4 from '@/assets/img/all-images/case/case-img4.png';
import element1 from '@/assets/img/elements/elements1.png';
import elements12 from '@/assets/img/elements/elements12.png';
import elements13 from '@/assets/img/elements/elements13.png';
import elements14 from '@/assets/img/elements/elements14.png';
import elements15 from '@/assets/img/elements/elements15.png';
import element16 from '@/assets/img/elements/elements16.png';
import icon13 from '@/assets/img/icons/service-icon13.svg';
import icon14 from '@/assets/img/icons/service-icon14.svg';
import icon15 from '@/assets/img/icons/service-icon15.svg';
import icon16 from '@/assets/img/icons/service-icon16.svg';
import logo4 from '@/assets/img/icons/sub-logo4.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
const Solutions = () => {
  return <>
      <div className="work4-section-area sp1">
        <Image src={element16} alt="" className="elements16" />
        <Image src={element1} alt="" className="elements1 keyframe5" />
        <Container>
          <Container>
            <Row>
              <Col lg={6} className="m-auto">
                <div className="heading6 text-center space-margin60">
                  <h5>
                    <Image src={logo4} alt="" />
                    how it works
                  </h5>
                  <div className="space18" />
                  <h2 className="text-anime-style-3">How Our IT Solutions Work</h2>
                </div>
              </Col>
            </Row>
            <Row>
              <Col lg={12}>
                <div className="others-widget-area">
                  <Row className="align-items-center">
                    <Col lg={4}>
                      <div className="card-boxarea box1" data-aos="zoom-in" data-aos-duration={800}>
                        <div className="div text-end">
                          <h3>
                            <Image src={icon13} alt="" />
                            <span className="d-none">our service</span>
                          </h3>
                        </div>
                        <div className="content-area">
                          <Link href="/service-single">Discovery Consultation</Link>
                          <div className="space14" />
                          <p>With our intuitive interface and robust features, you buy, sell.</p>
                        </div>
                      </div>
                      <div className="space30" />
                      <div className="card-boxarea box2" data-aos="zoom-in" data-aos-duration={900}>
                        <div className="content-area">
                          <Link href="/service-single">Customized IT Strategy</Link>
                          <div className="space14" />
                          <p>Plus, our commitment to the security means that PayCoin.</p>
                        </div>
                        <div className="div text-end">
                          <h3>
                            <Image src={icon14} alt="" />
                            <span className="d-none">our service</span>
                          </h3>
                        </div>
                      </div>
                    </Col>
                    <Col lg={4}>
                      <div className="images" data-aos="zoom-in" data-aos-duration={1000}>
                        <Image src={elements12} alt="" className="elements12" />
                        <Image src={elements13} alt="" className="elements13" />
                        <Image src={elements14} alt="" className="elements14" />
                        <Image src={elements15} alt="" className="elements15" />
                        <div className="img1">
                          <Image src={case4} alt="" />
                        </div>
                      </div>
                    </Col>
                    <Col lg={4}>
                      <div className="card-boxarea box3" data-aos="zoom-in" data-aos-duration={800}>
                        <div className="div">
                          <h3>
                            <Image src={icon15} alt="" />
                            <span className="d-none">Our Service</span>
                          </h3>
                        </div>
                        <div className="content-area2">
                          <Link href="/service-single">Quality Assurance</Link>
                          <div className="space14" />
                          <p>Trusted partner in the world of crypto trading Join us today.</p>
                        </div>
                      </div>
                      <div className="space30" />
                      <div className="card-boxarea box4" data-aos="zoom-in" data-aos-duration={1000}>
                        <div className="content-area2">
                          <Link href="/service-single">24/7 Support</Link>
                          <div className="space14" />
                          <p>Resources designed to help navigate best the dynamic</p>
                        </div>
                        <div className="div">
                          <h3>
                            <Image src={icon16} alt="" />
                            <span className="d-none">our service</span>
                          </h3>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Container>
        </Container>
      </div>
    </>;
};
export default Solutions;