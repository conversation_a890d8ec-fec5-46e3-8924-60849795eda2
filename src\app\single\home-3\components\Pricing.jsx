'use client';

import element30 from '@/assets/img/elements/elements30.png';
import check4 from '@/assets/img/icons/check4.svg';
import logo3 from '@/assets/img/icons/sub-logo3.svg';
import useToggle from '@/hook/useToggle';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { Col, Container, Row } from 'react-bootstrap';
const links = [{
  image: check4,
  title: 'All Message In One Dashboard'
}, {
  image: check4,
  title: 'Customer Data In Tickets'
}, {
  image: check4,
  title: 'Automation’s to Finish tasks'
}, {
  image: check4,
  title: 'Efficient Team Management'
}, {
  image: check4,
  title: 'Real Time Progress Reporting'
}, {
  image: check4,
  title: 'Dedicated Account Manager'
}];
const pricingmonthly = [{
  title: 'Basic Plan',
  description: 'Perfect for growing businesses that A need more flexibility and the features. ',
  count: '49'
}, {
  title: 'Team Plan',
  description: 'Perfect for growing businesses that A need more flexibility and the features.',
  count: '60'
}, {
  title: 'Business plan',
  description: 'Perfect for growing businesses that A need more flexibility and the features.',
  count: '99'
}];
const pricingyear = [{
  title: 'Basic Plan',
  description: 'Perfect for growing businesses that A need more flexibility and the features. ',
  count: '99'
}, {
  title: 'Team Plan',
  description: 'Perfect for growing businesses that A need more flexibility and the features.',
  count: '199'
}, {
  title: 'Business plan',
  description: 'Perfect for growing businesses that A need more flexibility and the features.',
  count: '399'
}];
const Pricing = () => {
  const {
    isOpen,
    toggle
  } = useToggle();
  const [isMonthly, setIsMonthly] = useState(true);
  const handleToggleChange = () => {
    setIsMonthly(prevState => !prevState);
  };
  return <>
      <div className="pricingplan1-section-area sp2" id="pricing">
        <Image src={element30} alt="" className="elements30" />
        <Container>
          <Row>
            <Col lg={5} className="m-auto">
              <div className="pricing-header heading5 text-center">
                <h5>
                  <Image src={logo3} alt="" />
                  Pricing Plan
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">
                  Find The Perfect Plan <br className="d-lg-block d-none" /> For Your Business
                </h2>
              </div>
              <div className="space24" />
            </Col>
          </Row>
          <Row>
            <Col xs={12} className="text-center">
              <div className={`plan-toggle-wrap space-margin60 ${isOpen ? 'active' : ''}`} style={{
              marginTop: 0
            }}>
                <div className="toggle-inner">
                  <input id="ce-toggle" onClick={toggle} checked={isMonthly} onChange={handleToggleChange} type="checkbox" />
                  <span className="custom-toggle" />
                  <div className="t-month">
                    <h4>Monthly</h4>
                  </div>
                  <div className="t-year">
                    <h4>Yearly</h4>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={12}>
              <div className="tab-content">
                {isMonthly ? <div id="monthly">
                    <Row>
                      {pricingmonthly.map((item, idx) => <Col lg={4} md={6} key={idx} data-aos="flip-right" data-aos-duration={800}>
                          <div className="single-pricing-area">
                            <div className="pricing-box">
                              <h3>{item.title}</h3>
                              <div className="space14" />
                              <p>{item.description}</p>
                              <div className="space12" />
                              <ul className="p-0">
                                {links.map((item, idx) => <li key={idx}>
                                    <Image src={item.image} alt="" />
                                    {item.title}
                                  </li>)}
                              </ul>
                              <div className="space28" />
                              <h2>
                                ${item.count} <span>/Monthly</span>
                              </h2>
                              <div className="space24" />
                              <div className="btn-area1">
                                <Link href="#" className="vl-btn4">
                                  Choose A Plan
                                </Link>
                              </div>
                            </div>
                          </div>
                        </Col>)}
                    </Row>
                  </div> : <div id="yearly">
                    <Row>
                      {pricingyear.map((item, idx) => <Col lg={4} md={6} key={idx} data-aos="flip-right" data-aos-duration={800}>
                          <div className="single-pricing-area">
                            <div className="pricing-box">
                              <h3>{item.title}</h3>
                              <div className="space14" />
                              <p>{item.description}</p>
                              <div className="space12" />
                              <ul className="p-0">
                                {links.map((item, idx) => <li key={idx}>
                                    <Image src={item.image} alt="" />
                                    {item.title}
                                  </li>)}
                              </ul>
                              <div className="space28" />
                              <h2>
                                ${item.count} <span>/Monthly</span>
                              </h2>
                              <div className="space24" />
                              <div className="btn-area1">
                                <Link href="#" className="vl-btn4">
                                  Choose A Plan
                                </Link>
                              </div>
                            </div>
                          </div>
                        </Col>)}
                    </Row>
                  </div>}
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Pricing;