'use client';
import logo6 from '@/assets/img/logo/logo.webp';
import MobileMenu from '@/components/layout/TopBar/components/MobileMenu';
import useScrollEvent from '@/hook/useScrollEvent';
import useToggle from '@/hook/useToggle';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaAngleDown, FaAngleRight, FaArrowRight, FaBarsStaggered } from 'react-icons/fa6';
const Header = () => {
  const {
    scrollY
  } = useScrollEvent();
  const {
    isOpen: openMobilMenu,
    toggle: toggleMobilMenu
  } = useToggle();
  return <>
    <header className="homepage5-body">
      <div id="vl-header-sticky" className={`vl-header-area vl-transparent-header header-${scrollY > 100 && 'sticky'}`}>
        <Container fluid className="headerfix">
          <Row className="align-items-center row-bg4">
            <Col lg={2} md={6} xs={6}>
              <div className="vl-logo">
                <Link href="/">
                  <Image src={logo6} alt="" />
                </Link>
              </div>
            </Col>
            <Col lg={7} className="d-none d-lg-block">
              <div className="vl-main-menu text-center">
                <nav className="vl-mobile-menu-active">
                  <ul className='p-0 m-0'>
                    <li className="has-dropdown">
                      <Link href="/">
                        Home
                      </Link>
                    </li>
                    <li className="has-dropdown">
                      <Link href="/about">
                        About Us
                      </Link>
                    </li>
                    <li>
                      <Link href="">
                        Services
                        <span>
                          <FaAngleDown className="fa-solid d-lg-inline d-none m-1" />
                        </span>
                      </Link>
                      <ul className="sub-menu">
                        <li>
                          <Link href="/service">Web Application</Link>
                        </li>
                        <li>
                          <Link href="/service">Website Development</Link>
                        </li>
                        <li>
                          <Link href="/service">App Development</Link>
                        </li>
                        <li>
                          <Link href="/service">Digital Marketing</Link>
                        </li>
                        <li>
                          <Link href="/service">Graphic Design</Link>
                        </li>

                      </ul>
                    </li>
                    <li>
                      <Link href="">
                        Case Study
                      </Link>

                    </li>
                    <li>
                      <Link href="">
                        Blogs
                      </Link>
                    </li>
                    <li>
                      <Link href="/contact">Contact</Link>
                    </li>
                  </ul>
                </nav>
              </div>
            </Col>
            <Col lg={3} md={6} xs={6}>
              <div className="vl-hero-btn d-none d-lg-block text-end">
                <span className="vl-btn-wrap text-end">
                  <Link className="vl-btn6" href="/contact">
                    Let’s Build Your App
                    <span className="arrow1">
                      <FaArrowRight className="fa-solid" />
                    </span>
                    <span className="arrow2">
                      <FaArrowRight className="fa-solid" />
                    </span>
                  </Link>
                </span>
              </div>
              <div className="vl-header-action-item d-block d-lg-none">
                <button onClick={toggleMobilMenu} type="button" className="vl-offcanvas-toggle">
                  <FaBarsStaggered />
                </button>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
      <MobileMenu toggleMobilMenu={toggleMobilMenu} openMobilMenu={openMobilMenu} />
    </header>
  </>;
};
export default Header;