'use client';

import bg6 from '@/assets/img/all-images/bg/hero-bg6.png';
import icon17 from '@/assets/img/icons/service-icon17.svg';
import logo4 from '@/assets/img/icons/sub-logo1.svg';
import Image from 'next/image';
import Link from 'next/link';
import { useRef } from 'react';
import { Col, Container, Row } from 'react-bootstrap';
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa6';
import Slider from 'react-slick';
const service = [{
  image: icon17,
  title: 'Custom Edit Tool',
  description: 'Design your page in real time and see the results instantly. Create an customize your all landing pages.'
}, {
  image: icon17,
  title: 'Easy To Customize',
  description: 'We bring your ideas to life with mobile applications designed to deliver results our team of expert.'
}, {
  image: icon17,
  title: 'Built In Safety Chat',
  description: 'Ensuring your app reaches its full potential on iOS, Android, or both. From the initial concept to post.'
}, {
  image: icon17,
  title: 'Custom Edit Tool',
  description: 'Design your page in real time and see the results instantly. Create an customize your all landing pages.'
}, {
  image: icon17,
  title: 'Custom Edit Tool',
  description: 'We bring your ideas to life with mobile applications designed to deliver results our team of expert.'
}, {
  image: icon17,
  title: 'Custom Edit Tool',
  description: 'Ensuring your app reaches its full potential on iOS, Android, or both. From the initial concept to post.'
}];
const Service = () => {
  const sliderRef = useRef(null);
  const settings = {
    dots: false,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 2000,
    arrows: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    responsive: [{
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }]
  };
  return <>
      <div className="service5-section-area img-fluid" style={{
      backgroundImage: `url(${bg6.src})`,
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center',
      backgroundSize: 'cover',
      height: '630px'
    }}>
        <Container>
          <Row>
            <Col lg={6}>
              <div className="service-header heading7 space-margin60">
                <h5>
                  <span>
                    <Image src={logo4} alt="" />
                  </span>
                  Our Solution
                </h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">Our Best Bespoke Solution</h2>
              </div>
            </Col>
          </Row>
          <Row>
            <Col lg={12}>
              <div className="service5-slider-box owl-carousel">
                <Slider ref={sliderRef} {...settings}>
                  {service.map((item, idx) => <div key={idx} className="service-slider-box">
                      <div className="icons">
                        <Image src={item.image} alt="" />
                      </div>
                      <div className="space24" />
                      <div className="content-area">
                        <Link href="/service-single">{item.title}</Link>
                        <div className="space18" />
                        <p>{item.description}</p>
                      </div>
                    </div>)}
                </Slider>
                <div className="owl-nav">
                  <button className="owl-prev" onClick={() => sliderRef.current?.slickPrev()}>
                    <FaAngleLeft />
                  </button>
                  <button className="owl-next" onClick={() => sliderRef.current?.slickNext()}>
                    <FaAngleRight />
                  </button>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Service;