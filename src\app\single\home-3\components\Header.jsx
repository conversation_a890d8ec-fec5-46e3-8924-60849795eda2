'use client';

import logo4 from '@/assets/img/logo/logo4.png';
import useActiveSection from '@/hook/useActiveSection';
import useScrollEvent from '@/hook/useScrollEvent';
import useToggle from '@/hook/useToggle';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaBarsStaggered } from 'react-icons/fa6';
import MobileMenu from './MobileMenu';
const sectionIds = ['about', 'work', 'support', 'features', 'pricing', 'faq'];
const Header = () => {
  const {
    scrollY
  } = useScrollEvent();
  const {
    isOpen: openMobilMenu,
    toggle: toggleMobilMenu
  } = useToggle();
  const activeSection = useActiveSection(sectionIds);
  return <>
      <header className="homepage3-body">
        <div id="vl-header-sticky" className={`vl-header-area vl-transparent-header header-${scrollY > 100 && 'sticky'}`}>
          <Container className="headerfix">
            <Row className="align-items-center row-bg">
              <Col lg={2} md={6} xs={6}>
                <div className="vl-logo">
                  <Link href="/">
                    <Image src={logo4} alt="" />
                  </Link>
                </div>
              </Col>
              <Col lg={7} className="d-none d-lg-block">
                <div className="vl-main-menu text-center">
                  <nav className="vl-mobile-menu-active navbar justify-content-center" id="navbar-example2">
                    <ul className="m-0 nav-pills">
                      {sectionIds.map(id => <li key={id} className="nav-item">
                          <Link href={`#${id}`} className={`nav-link ${activeSection === id ? 'active' : ''}`}>
                            <span>{id.charAt(0).toUpperCase() + id.slice(1)}</span>
                          </Link>
                        </li>)}
                    </ul>
                  </nav>
                </div>
              </Col>
              <Col lg={3} md={6} xs={6}>
                <div className="vl-hero-btn d-none d-lg-block text-end">
                  <span className="vl-btn-wrap text-end">
                    <Link href="/contact" className="vl-btn4">
                      <span className="demo">Connect with Support</span>
                    </Link>
                  </span>
                </div>
                <div className="vl-header-action-item d-block d-lg-none">
                  <button onClick={toggleMobilMenu} type="button" className="vl-offcanvas-toggle">
                    <FaBarsStaggered className="fa-solid" />
                  </button>
                </div>
              </Col>
            </Row>
          </Container>
        </div>
        <MobileMenu toggleMobilMenu={toggleMobilMenu} openMobilMenu={openMobilMenu} />
      </header>
    </>;
};
export default Header;