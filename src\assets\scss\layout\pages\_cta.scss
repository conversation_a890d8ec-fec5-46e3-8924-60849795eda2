@use '../../utils/' as * ;

/*============= CTA CSS AREA ===============*/
.cta1-section-area {
    position: relative;
    z-index: 1;
    
    // background-image: url(img/all-images/bg/hero-bg2.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    overflow: hidden;
    @media #{$xs} {
        padding: 50px 0 0 !important;
    }
    @media #{$md} {
        padding: 50px 0 0 !important;
    }
    .cta-header {
        position: relative;
        z-index: 1;
        form {
            position: relative;
            z-index: 1;
            input {
                width: 100%;
                color: var(--Text-Color, #050734);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s20);
                font-style: normal;
                font-weight: var( --ztc-weight-medium);
                line-height: 20px; 
                background: var(--ztc-bg-bg-1);
                border-radius: 8px;
                padding: 26px 24px;
                &::placeholder {
                    color: var(--Text-Color, #050734);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var( --ztc-weight-medium);
                    line-height: 20px;
                }              
            }
            button {
                border: none;
                outline: none;
                border-radius: 8px;
                background: var(--Main-Color, linear-gradient(90deg, #2E0797 0%, #726EFC 100%));
                color: var(--ztc-text-text-1);
                position: absolute;
                right: 10px;
                top: 10px;
            }
        }
    }
    .cta-images {

        ul {
            position: absolute;
            top: 60px;
            right: 650px;
            @media #{$xxl} {
                right: 400px;
            }
            @media #{$xxxl} {
                right: 500px;
            }
            @media #{$md} {
                right: 400px;
                top: 400px;
            }
            li {
                a {
                    display: inline-block;
                    color: var(--ztc-text-text-2);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 16px; 
                    background: var(--ztc-bg-bg-1);
                    border-radius: 8px;
                    padding: 12px;
                    margin-bottom: 24px;
                }
                img {
                    margin: 0 4px 0 0;
                }  
                &:nth-child(2) {
                    margin-left: -40px;
                }            
            }
        }
        .img1 {
            position: absolute;
            bottom: 0;
            @media #{$xs} {
                position: relative;
                margin-top: 30px;
            }
            @media #{$md} {
                position: relative;
                margin-top: 30px;
            }
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }
        .elements7 {
            position: absolute;
            z-index: -1;
            bottom: 0;
        }
        .elements8 {
            position: absolute;
            z-index: -1;
            top: 0;
            right: 650px;
            @media #{$xxl} {
                right: 400px;
            }
            @media #{$xxxl} {
                right: 500px;
            }
            @media #{$md} {
                right: 400px;
                top: 400px;
            }
        }
    }
}

// Homepage 02 //
.cta2-section-area {
    position: relative;
    z-index: 1;
    // background-image: url(../../../img/all-images/bg/cta-bg1.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;;
    overflow: hidden;
    @media #{$xs} {
        padding: 50px 0 0 !important;
    }
    @media #{$md} {
        padding: 50px 0 0 !important;
    }
    .cta-header {
        position: relative;
        z-index: 1;
        h2 {
            color: var(--ztc-text-text-2);
        }
        p {
            color: var(--ztc-text-text-3);
        }
        form {
            position: relative;
            z-index: 1;
            input {
                width: 100%;
                color: var(--ztc-text-text-6);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s20);
                font-style: normal;
                font-weight: var( --ztc-weight-medium);
                line-height: 20px; 
                background: #AEDC42;
                border-radius: 68px;
                padding: 26px 24px;
                &::placeholder {
                    color: var(--ztc-text-text-6);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var( --ztc-weight-medium);
                    line-height: 20px;
                }              
            }
            button {
                border: none;
                outline: none;
                position: absolute;
                background: none;
                right: 10px;
                top: 10px;
                span {
                    background: var(--ztc-bg-bg-1);
                    color: var(--ztc-text-text-6);
                }
            }
        }
    }
    .cta-images {
        .img1 {
            position: absolute;
            bottom: 0;
            @media #{$xs} {
                position: relative;
                margin-top: 30px;
            }
            @media #{$md} {
                position: relative;
                margin-top: 30px;
            }
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }
        .elements7 {
            position: absolute;
            z-index: -1;
            bottom: 0;
            filter: brightness(0.4);
        }
        ul {
            position: absolute;
            top: 60px;
            right: 650px;
            @media #{$xxl} {
                right: 400px;
            }
            @media #{$xxxl} {
                right: 500px;
            }
            @media #{$md} {
                right: 400px;
                top: 400px;
            }
            li {
                a {
                    display: inline-block;
                    color: var(--ztc-text-text-6);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 16px; 
                    background: var(--ztc-bg-bg-1);
                    border-radius: 8px;
                    padding: 12px;
                    margin-bottom: 24px;
                }
                img {
                    margin: 0 4px 0 0;
                }  
                &:nth-child(2) {
                    margin-left: -40px;
                }            
            }
        }
    }
}

// Homepage 02 //
.cta3-section-area {
    position: relative;
    z-index: 2;
    margin-bottom: -170px;
    .cta-bg-area {
        background: var(--ztc-bg-bg-9);
        overflow: hidden;
        padding: 70px;
        border-radius: 8px;
        position: relative;
        @media #{$xs} {
            padding: 30px;
        }
        .elements26 {
            position: absolute;
            opacity: 10%;
            top: 0;
            left: 50%;
            margin-left: -150px;
        }
        .cta-header {
            position: relative;
            z-index: 1;
            h2 {
                color: var(--ztc-text-text-1);
            }
            p {
                color: var(--ztc-text-text-1);
                opacity: 80%;
            }
            .btn-area1 {
                a {
                    background: var(--ztc-bg-bg-1);
                    color: var(--ztc-text-text-9);
                    &::after {
                        background: var(--ztc-bg-bg-9);
                        opacity: 10%;
                    }
                    &:hover {
                        color: var(--ztc-text-text-1);
                        transition: all .4s;
                        &::after {
                            background: var(--ztc-bg-bg-1);
                            opacity: 10%;
                        }
                    }
                }

                a.bnt2 {
                    background: var(--ztc-bg-bg-8);
                    color: var(--ztc-text-text-1);
                    margin-left: 16px;
                    @media #{$xs} {
                        margin-left: 0;
                        margin-top: 20px;
                    }
                    @media #{$md} {
                        margin-left: 0;
                        margin-top: 20px;
                    }
                    &::after {
                        background: var(--ztc-bg-bg-1);
                        opacity: 10%;
                    }
                    &::before {
                        background: var(--ztc-bg-bg-1);
                        transition: all .4s;
                    }
                    &:hover {
                        color: var(--ztc-text-text-9);
                        transition: all .4s;
                        &::after {
                            background: var(--ztc-bg-bg-8);
                            opacity: 10%;
                        }
                    }
                }
            }
        }
    }
}

// Homepage 02 //
.cta4-section-area {
    position: relative;
    z-index: 1;
    // background-image: url(../../../img/all-images/bg/hero-bg5.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;;
    overflow: hidden;
    @media #{$xs} {
        padding: 50px 0 0 !important;
    }
    @media #{$md} {
        padding: 50px 0 0 !important;
    }
    .elements33 {
        position: absolute;
        bottom: 0;
    }
    .elements1 {
        position: absolute;
        right: 150px;
        @media #{$xs} {
            left: 0;
            right: inherit;
            top: 295px;
        }
        @media #{$md} {
            top: 26%;
        }
    }
    .elements16 {
        position: absolute;
        right: -100px;
        @media #{$xs} {
            top: 35%;
        }
        @media #{$md} {
            top: 27%;
        }
    }
    .cta-header {
        position: relative;
        z-index: 1;
        h2 {
            color: var(--ztc-text-text-1);
        }
        p {
            color: var(--ztc-text-text-1);
            opacity: 80%;
        }
        form {
            position: relative;
            z-index: 1;
            input {
                width: 100%;
                color: var(--ztc-text-text-10);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s20);
                font-style: normal;
                font-weight: var( --ztc-weight-medium);
                line-height: 20px; 
                background: var(--ztc-bg-bg-1);
                border-radius: 68px;
                padding: 26px 24px;
                &::placeholder {
                    color: var(--ztc-text-text-11);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var( --ztc-weight-medium);
                    line-height: 20px;
                }              
            }
            button {
                border: none;
                outline: none;
                position: absolute;
                background: none;
                right: 10px;
                top: 10px;
                
            }
        }
    }
    .cta-images {
        .img1 {
            position: absolute;
            bottom: -100px;
            margin-left: 35px;
            @media #{$xs} {
                position: relative;
                margin-left: 0;
            }
            @media #{$md} {
                position: relative;
            }
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }
        .elements36 {
            position: absolute !important;
            z-index: -1;
            top: 80px;
            @media #{$xs} {
                top: 460px;
            }
        }
        ul {
            position: absolute;
            top: 60px;
            right: 650px;
            @media #{$xxl} {
                right: 400px;
            }
            @media #{$xxxl} {
                right: 500px;
            }
            @media #{$md} {
                right: 400px;
                top: 400px;
            }
            li {
                a {
                    display: inline-block;
                    color: var(--ztc-text-text-6);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 16px; 
                    background: var(--ztc-bg-bg-1);
                    border-radius: 8px;
                    padding: 12px;
                    margin-bottom: 24px;
                }
                img {
                    margin: 0 4px 0 0;
                }  
                &:nth-child(2) {
                    margin-left: -40px;
                }            
            }
        }
    }
}

// Homepage 05 //
.cta5-section-area{
    position: relative;
    z-index: 1;
    margin: 0 30px;
    border-radius: 16px;
    overflow: hidden;
    @media #{$xs} {
        margin: 0;
        border-radius: 0;
    }
    @media #{$md} {
        margin: 0;
        border-radius: 0;
    }
    .cta-header {
        @media #{$xs} {
            padding-top: 50px;
        }
        @media #{$md} {
            padding-top: 50px;
        }
      .btn-area1 {
        .vl-btn6 {
            background: var(--ztc-bg-bg-1);
            color: var(--ztc-text-text-14);
            &:hover {
                color: var(--ztc-text-text-1);
            }
        }
        .vl-btn6.btn2 {
            background: var(--ztc-bg-bg-13);
            color: var(--ztc-text-text-1);
            margin-left: 16px;
            @media #{$xs} {
                margin-left: 0;
                margin-top: 16px;
            }
            &:hover {
                color: var(--ztc-text-text-1);
            }
        }
      }  
    }
    .images-area {
        position: relative;
        top: 80px;
        .img1 {
            transform: rotate(-10deg);
            @media #{$xs} {
                transform: rotate(0);
            }
            @media #{$md} {
                transform: rotate(0);
            }
        }
        
        .img2 {
            position: relative;
            transform: rotate(10deg);
            @media #{$xs} {
                transform: rotate(0);
            }
            @media #{$md} {
                transform: rotate(0);
            }
        }
    }
}
/*============= CTA CSS AREA ENDS===============*/