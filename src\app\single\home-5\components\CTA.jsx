import bg6 from '@/assets/img/all-images/bg/hero-bg6.png';
import img7 from '@/assets/img/all-images/hero/hero-img7.png';
import logo1 from '@/assets/img/icons/sub-logo1.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const CTA = () => {
  return <>
      <div className="cta5-section-area" style={{
      backgroundImage: `url(${bg6.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Container>
          <Row className="align-items-center">
            <Col lg={5}>
              <div className="cta-header heading7">
                <h5 data-aos="fade-left" data-aos-duration={800}>
                  <span>
                    <Image src={logo1} alt="" />
                  </span>
                  Start Your Mobile App Journey Now
                </h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">Let’s Build Somethings Amazing App Together</h2>
                <div className="space16" />
                <p data-aos="fade-left" data-aos-duration={900}>
                  Don’t let your mobile app idea stay on the drawing board. At ETech, we’re ready to bring your vision life innovative.
                </p>
                <div className="space32" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1000}>
                  <Link className="vl-btn6" href="/contact">
                    Let’s Build Your App
                    <span className="arrow1">
                      <FaArrowRight />
                    </span>
                    <span className="arrow2">
                      <FaArrowRight />
                    </span>
                  </Link>
                  <Link className="vl-btn6 btn2" href="/contact">
                    Start Project
                    <span className="arrow1">
                      <FaArrowRight />
                    </span>
                    <span className="arrow2">
                      <FaArrowRight />
                    </span>
                  </Link>
                </div>
              </div>
            </Col>
            <Col lg={1} />
            <Col lg={6}>
              <div className="images-area">
                <Row>
                  <Col lg={6} md={6} data-aos="fade-right" data-aos-duration={1000}>
                    <div className="img1">
                      <Image src={img7} alt="" width={408} height={650} className='img-fluid' />
                    </div>
                    <div className="space30 d-md-none d-block" />
                  </Col>
                  <Col lg={6} md={6} data-aos="fade-left" data-aos-duration={1000}>
                    <div className="img2">
                      <Image src={img7} alt="" width={408} height={650} className='img-fluid' />
                    </div>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default CTA;