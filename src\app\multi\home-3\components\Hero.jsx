import hero4 from '@/assets/img/all-images/bg/hero-bg4.png';
import hero04 from '@/assets/img/all-images/hero/hero-img4.png';
import hero05 from '@/assets/img/all-images/hero/hero-img5.png';
import element24 from '@/assets/img/elements/elements24.png';
import element25 from '@/assets/img/elements/elements25.png';
import icon1 from '@/assets/img/icons/s-icon1.svg';
import icon10 from '@/assets/img/icons/s-icon10.svg';
import icon11 from '@/assets/img/icons/s-icon11.svg';
import icon2 from '@/assets/img/icons/s-icon2.svg';
import icon3 from '@/assets/img/icons/s-icon3.svg';
import icon4 from '@/assets/img/icons/s-icon4.svg';
import icon5 from '@/assets/img/icons/s-icon5.svg';
import icon6 from '@/assets/img/icons/s-icon6.svg';
import icon7 from '@/assets/img/icons/s-icon7.svg';
import icon8 from '@/assets/img/icons/s-icon8.svg';
import icon9 from '@/assets/img/icons/s-icon9.svg';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
const icons = [{
  icon: icon1
}, {
  icon: icon2
}, {
  icon: icon3
}, {
  icon: icon4
}, {
  icon: icon5
}, {
  icon: icon6
}, {
  icon: icon7
}, {
  icon: icon8
}, {
  icon: icon9
}, {
  icon: icon10
}, {
  icon: icon11
}];
const Hero = () => {
  return <>
      <div className="hero3-section-area" style={{
      backgroundImage: `url(${hero4.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Image src={element24} alt="" className="elements24" />
        <Image src={element25} alt="" className="elements25" />
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <div className="hero3-header heading3">
                <h5 data-aos="fade-left" data-aos-duration={800}>
                  <Image src={logo2} alt="" />
                  Support That Works for You, Anytime
                </h5>
                <div className="space24" />
                <h1 className="text-anime-style-3">Your Help Desk Solution: Fast, Reliable, And Ready</h1>
                <div className="space18" />
                <p data-aos="fade-left" data-aos-duration={900}>
                  At our help desk, we’re dedicated providing exceptional support whenever you need it. Our team of experts is available 24/7 to
                  assist with any issues.
                </p>
                <div className="space24" />
                <form data-aos="fade-left" data-aos-duration={1000}>
                  <input type="text" placeholder="Your Email Address" />
                  <button className="vl-btn4" type="submit">
                    Request A Support
                  </button>
                </form>
                <div className="space24" />
                <h4 data-aos="fade-left" data-aos-duration={1100}>
                  Support Any Chanel With On Platform
                </h4>
                <div className="space24" />
                <ul className="p-0">
                  {icons.map((item, idx) => <li key={idx} style={{
                  paddingLeft: '3px'
                }}>
                      <Link href="">
                        <Image src={item.icon} alt="" />
                      </Link>
                    </li>)}
                </ul>
              </div>
            </Col>
            <Col lh={2} />
            <Col lg={4}>
              <div className="header-images-area">
                <div className="img1" data-aos="zoom-in" data-aos-duration={1000}>
                  <Image src={hero04} alt="" />
                  <div className="letter1">
                    <svg xmlns="http://www.w3.org/2000/svg" width={22} height={22} viewBox="0 0 22 22" fill="none">
                      <path d="M21.1143 1.94118C21.4025 1.26041 20.7492 0.559816 20.0544 0.804604L0.801081 7.58762C0.223207 7.79121 0.0442074 8.52444 0.4628 8.97333L11.7284 21.0542C12.147 21.5031 12.8869 21.3713 13.1266 20.8051L21.1143 1.94118Z" fill="white" />
                    </svg>
                    <p>I Have Need Help!</p>
                  </div>
                  <div className="letter2">
                    <svg xmlns="http://www.w3.org/2000/svg" width={20} height={23} viewBox="0 0 20 23" fill="none">
                      <path d="M1.77654 0.585636C1.25754 0.211338 0.542077 0.636694 0.624195 1.27073L3.33048 22.166C3.39796 22.687 3.97755 22.9687 4.4293 22.7002L18.8273 14.1402C19.2791 13.8717 19.3073 13.2285 18.8808 12.921L1.77654 0.585636Z" fill="white" />
                    </svg>
                    <p>Thanks For Your Support</p>
                  </div>
                  <div className="letter3">
                    <svg xmlns="http://www.w3.org/2000/svg" width={18} height={24} viewBox="0 0 18 24" fill="none">
                      <path d="M1.0605 22.4625C1.06572 23.1001 1.8308 23.4228 2.29486 22.9831L17.5361 8.54245C17.9185 8.18014 17.8041 7.54652 17.3193 7.34209L1.9067 0.841864C1.42196 0.637427 0.884826 0.996253 0.889126 1.52164L1.0605 22.4625Z" fill="white" />
                    </svg>
                    <p>Hello! Iam Alex Roy</p>
                  </div>
                </div>
                <div className="img2" data-aos="zoom-in" data-aos-duration={1200}>
                  <Image src={hero05} alt="" />
                  <div className="letter1">
                    <svg xmlns="http://www.w3.org/2000/svg" width={22} height={22} viewBox="0 0 22 22" fill="none">
                      <path d="M21.1143 1.94118C21.4025 1.26041 20.7492 0.559816 20.0544 0.804604L0.801081 7.58762C0.223207 7.79121 0.0442074 8.52444 0.4628 8.97333L11.7284 21.0542C12.147 21.5031 12.8869 21.3713 13.1266 20.8051L21.1143 1.94118Z" fill="white" />
                    </svg>
                    <p>Hello I Am There</p>
                  </div>
                  <div className="letter2">
                    <svg xmlns="http://www.w3.org/2000/svg" width={21} height={21} viewBox="0 0 21 21" fill="none">
                      <path d="M20.8936 1.5779C21.1818 0.897132 20.5285 0.196535 19.8337 0.441323L0.580378 7.22434C0.00250406 7.42793 -0.176496 8.16116 0.242097 8.61004L11.5077 20.691C11.9263 21.1399 12.6661 21.008 12.9059 20.4418L20.8936 1.5779Z" fill="white" />
                    </svg>
                    <p>We are happy to help you!</p>
                  </div>
                  <div className="letter3">
                    <svg xmlns="http://www.w3.org/2000/svg" width={20} height={23} viewBox="0 0 20 23" fill="none">
                      <path d="M1.75897 0.718448C1.23996 0.34415 0.524499 0.769507 0.606617 1.40354L3.3129 22.2988C3.38038 22.8198 3.95997 23.1015 4.41172 22.833L18.8097 14.273C19.2615 14.0045 19.2897 13.3614 18.8633 13.0538L1.75897 0.718448Z" fill="white" />
                    </svg>
                    <p>Tell us What your need</p>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Hero;