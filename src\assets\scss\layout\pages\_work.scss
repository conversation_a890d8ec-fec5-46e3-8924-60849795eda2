@use '../../utils/' as * ;

/*============= WORK CSS AREA ===============*/
.work1-section-area {
    position: relative;
    z-index: 1;
    .elements9 {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
    }
    .solution-header {
        .bg-progress {
            background: #F2F4FF;
            border-radius: 8px;
            padding: 24px;
            .progress-bar {
                margin-bottom: 20px;
            }
            
            label {
                color: var(--ztc-text-text-2);
                font-family: var(--ztc-family-font1);
                font-size: variable-exists($name: --ztc-font-size-font-s18);
                font-style: normal;
                font-weight: var(--ztc-weight-semibold);
                line-height: 18px;
                display: flex;
                justify-content: space-between;
                margin-bottom: 14px;
            }
            
            .progress {
                background-color: #DADCEB;
                border-radius: 20px;
                height: 10px;
                position: relative;
            }
            
            .progress-inner {
                border-radius: 40px;
                background: var(--Main-Color, linear-gradient(90deg, #2E0797 0%, #726EFC 100%));
                height: 100%;
                transition: width 0.4s ease;
            }
        }
    }
    .soultions-images {
        position: relative;
        z-index: 1;
        @media #{$xs} {
            padding-top: 50px;
        }
        @media #{$md} {
            padding-top: 50px;
        }
        .elements10 {
            position: absolute;
            left: -50px;
            top: -50px;
            @media #{$md} {
                right: -50px;
                left: inherit;
                top: 0;
            }
            @media #{$xs} {
                display: none;
            }
        }
        .img2 {
            position: relative;
            z-index: 1;
            margin-top: -250px;
            margin-left: -150px;
            height: 330px;
            width: 400px;
            @media #{$md} {
                margin-left: 0;
            }
            @media #{$xs} {
                margin-left: 0;
                margin-top: 30px;
            }
            @media #{$xs} {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 8px;
            }
            img {
                height: 330px;
                width: 400px;
                @media #{$xs} {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 8px;
                }
            }
        }
    }
}
.work-others-section {
    position: relative;
    z-index: 1;
    padding: 100px 0 280px;
    .elements16 {
        position: absolute;
        right: -70px;
        top: 0;
    }
    .elements1 {
        position: absolute;
        right: 180px;
        top: 20px;
        z-index: -1;
    }
}

// Homepage 02 //
.work2-section-area {
    position: relative;
    z-index: 1;
    // background-image: url(../../../img/all-images/bg/footer-bg1.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    .elements9 {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
    }

    .soultions-images {
        position: relative;
        z-index: 1;
        padding: 0 50px 0 0;
        @media #{$xs} {
            padding: 0;
            margin-bottom: 50px;
        }
        @media #{$md} {
            padding: 0;
            margin-bottom: 50px;
        }
        .img1 {
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 200px 200px 0 0;
            }
        }

        .arrow-circle {
            a {
                height: 160px;
                width: 160px;
                display: inline-block;
                transition: all .4s;
                border-radius: 50%;
                background: var(--ztc-bg-bg-6);
                position: absolute;
                bottom: 0;
                left: 40%;
                z-index: 1;
                .arrow1 {
                    position: absolute;
                    top: 41%;
                    left: 44%;
                }  
                .elements20 {
                    position: absolute;
                    top: 6px;
                    left: 6px;
                } 
            }
        }
        .img2 {
            position: relative;

            .star {
                position: absolute;
                left: -30px;
                z-index: 2;
                top: 20px;
                @media #{$xs} {
                    left: 40%;
                }
                img {
                    height: 60px;
                    width: 60px;
                    object-fit: contain;
                }
            }
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 0 0 200px 200px;
            }
        }
    }
    .solution-header {
        .bg-progress {
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.06);
            padding: 24px;
            .progress-bar {
                margin-bottom: 24px;
            }
            
            label {
                color: var(--ztc-text-text-1);
                font-family: var(--ztc-family-font1);
                font-size: variable-exists($name: --ztc-font-size-font-s18);
                font-style: normal;
                font-weight: var(--ztc-weight-semibold);
                line-height: 18px;
                display: flex;
                justify-content: space-between;
                margin-bottom: 14px;
            }
            
            .progress {
                background-color: #344F57;
                border-radius: 20px;
                height: 10px;
                position: relative;
            }
            
            .progress-inner {
                border-radius: 40px;
                background: var(--ztc-bg-bg-6);
                height: 100%;
                transition: width 0.4s ease;
            }
        }
    }
}

// Homepage 03 //
.works3-section-area {
    position: relative;
    z-index: 1;
    background: #EFF1FF;
    .works-single-boxarea {
        position: relative;
        z-index: 1;
        overflow: hidden;
        text-align: center;
        background: var(--ztc-bg-bg-1);
        padding: 32px 40px;
        border-radius: 8px;
        margin-bottom: 38px;
        @media #{$md} {
            padding: 30px;
        }
        &::after {
            position: absolute;
            content: "";
            height: 100%;
            width: 10px;
            left: 50%;
            transition: all .4s;
            background: var(--ztc-bg-bg-9);
            top: 0;
            visibility: hidden;
            opacity: 0;
            border-radius: 8px;
            z-index: -1;
        }
        &:hover {
            &::after {
                visibility: visible;
                opacity: 1;
                transition: all .4s;
                width: 100%;
                left: 0;
            }
            .icons {
                background: var(--ztc-bg-bg-1);
                transition: all .4s;
                img {
                    filter: none;
                    transition: all .4s;
                }
            }
            .content-area {
                a {
                    color: var(--ztc-text-text-1);
                    transition: all .4s;
                }
                p {
                    color: var(--ztc-text-text-1);
                    opacity: 80%;
                }
            }
        }
        .icons {
            height: 80px;
            width: 80px;
            text-align: center;
            line-height: 80px;
            display: inline-block;
            transition: all .4s;
            border-radius: 50%;
            background: var(--ztc-bg-bg-9);
            img {
                filter: brightness(0) invert(1);
                transition: all .4s;
            }
        }
        .content-area {
            a {
                color: var(--ztc-text-text-7);
                text-align: center;
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s24);
                font-style: normal;
                font-weight: var(--ztc-weight-semibold);
                line-height: 24px;
                display: inline-block;
                transition: all .4s;       
            }
            p {
                color: var(--ztc-text-text-8);
                text-align: center;
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s18);
                font-style: normal;
                font-weight: var(--ztc-weight-medium);
                line-height: 26px;
                transition: all .4s;              
            }
        }
    }
}

// Homepage 04 //
.work4-section-area {
    position: relative;
    z-index: 1;
    overflow: hidden;
    .elements16 {
        position: absolute;
        left: -100px;
        transform: rotateY(-180deg);
        top: 0;
    }
    .elements1 {
        position: absolute;
        left: 140px;
        top: 0;
    }
    .others-widget-area {
        background: var(--ztc-bg-bg-1);
        border-radius: 16px;
        position: relative;
        .card-boxarea {
                &:hover {
                    h3 {
                        transform: rotateY(360deg);
                        transition: all .4s;
                    }
                }
            h3 {
                line-height: 50px;
                letter-spacing: -0.24px;    
                display: inline-block;
                transition: all .4s;
                border-radius: 50%;   
                height: 60px;
                width: 60px;
                text-align: center;
                background: #F2F4FF;   
                img {
                    height: 30px;
                    width: 30px;
                    display: inline-block;
                    text-align: center;
                }         
            }
            .content-area {
                background: #F2F4FF;
                padding: 28px;
                border-radius: 4px;
                text-align: center;
                margin: 0 40px 0 0;
                position: relative;
                z-index: 1;
                @media #{$xs} {
                    margin: 0;
                }
                &:hover {
                    &::after {
                        width: 100%;
                        left: 0;
                        top: 0;
                        transition: all .4s;
                        visibility: visible;
                        opacity: 1;
                    }
                    a {
                        color: var(--ztc-text-text-1);
                        transition: all .4s;
                    }
                    p {
                        color: var(--ztc-text-text-1);
                        transition: all .4s;
                        opacity: 80%;
                    }
                }
                &::after {
                    position: absolute;
                    content: "";
                    height: 100%;
                    width: 10px;
                    left: 50%;
                    transition: all .4s;
                    top: 0;
                    background: var(--ztc-bg-bg-10);
                    z-index: -1;
                    border-radius: 8px;
                    visibility: hidden;
                    opacity: 0;
                }
                a {
                    color: var(--ztc-text-text-10);
                    text-align: center;
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var(--ztc-weight-bold);
                    line-height: 20px;
                    display: inline-block;
                    transition: all .4s;                     
                }
                p {
                    color: var(--ztc-text-text-10);
                    text-align: center;
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 26px; /* 162.5% */
                    letter-spacing: 0.16px;  
                    opacity: 0.8;   
                    transition: all .4s;                  
                }
            }

            .content-area2 {
                background: #F2F4FF;
                padding: 28px;
                border-radius: 4px;
                text-align: center;
                margin: 0 0 0 40px;
                position: relative;
                z-index: 1;
                @media #{$xs} {
                    margin: 0;
                }
                &:hover {
                    &::after {
                        width: 100%;
                        left: 0;
                        top: 0;
                        transition: all .4s;
                        visibility: visible;
                        opacity: 1;
                    }
                    a {
                        color: var(--ztc-text-text-1);
                        transition: all .4s;
                    }
                    p {
                        color: var(--ztc-text-text-1);
                        transition: all .4s;
                        opacity: 80%;
                    }
                   
                }
                &::after {
                    position: absolute;
                    content: "";
                    height: 100%;
                    width: 10px;
                    left: 50%;
                    transition: all .4s;
                    top: 0;
                    background: var(--ztc-bg-bg-10);
                    z-index: -1;
                    border-radius: 8px;
                    visibility: hidden;
                    opacity: 0;
                }
                a {
                    color: var(--ztc-text-text-10);
                    text-align: center;
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var(--ztc-weight-bold);
                    line-height: 20px;
                    display: inline-block;
                    transition: all .4s;                     
                }
                p {
                    color: var(--ztc-text-text-10);
                    text-align: center;
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 26px; /* 162.5% */
                    letter-spacing: 0.16px;  
                    opacity: 0.8;   
                    transition: all .4s;                  
                }
            }
            &.box1 {
                h3 {
                    position: relative;
                    left: -75px;
                    margin-bottom: 20px;
                    @media #{$xs} {
                        left: 0;
                    }
                }
            }
            &.box2 {
                h3 {
                    position: relative;
                    left: -73px;
                    margin-top: 13px;
                    @media #{$xs} {
                        left: 0;
                    }
                }
            }

            &.box3 {
                h3 {
                    position: relative;
                    left: 74px;
                    margin-top: -33px;
                    margin-bottom: 16px;
                    @media #{$xs} {
                        left: 0;
                    }
                }
            }
            &.box4 {
                h3 {
                    position: relative;
                    left: 74px;
                    margin-top: 16px;
                    margin-bottom: -16px;
                    @media #{$xs} {
                        left: 0;
                    }
                }
            }
        }
        .images {
            position: relative;
            z-index: 1;
            @media #{$xs} {
                margin-top: 30px;
                margin-bottom: 30px;
            }
            
            .elements12 {
                position: absolute;
                top: -10px;
                left: -100px;
                @media #{$xs} {
                    display: none;
                }
                @media #{$md} {
                    display: none;
                }
            }

            .elements13 {
                position: absolute;
                bottom: -10px;
                left: -100px;
                @media #{$xs} {
                    display: none;
                }
                @media #{$md} {
                    display: none;
                }
            }

            .elements14 {
                position: absolute;
                top: -10px;
                right: -100px;
                @media #{$xs} {
                    display: none;
                }
                @media #{$md} {
                    display: none;
                }
            }
            .elements15 {
                position: absolute;
                bottom: -10px;
                right: -100px;
                @media #{$xs} {
                    display: none;
                }
                @media #{$md} {
                    display: none;
                }
            }
            .img1 {
                position: relative;
                z-index: 1;
                background: #F1F0FE;
                padding: 16px;
                border-radius: 50%;
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 50%;

                }
            }
        }
    }
}

// Homepage 05 //
.work5-section-area {
    position: relative;
    z-index: 1;
    .work-widget-boxes {
        position: relative;
        z-index: 1;
        .work-boxarea {
            text-align: center;
            position: relative;
            z-index: 1;
            margin-bottom: 30px;
            @media #{$xs} {

            }
            &.box2 {
                &::after {
                    display: none;
                }
            }
            &::after {
                position: absolute;
                content: "";
                height: 2px;
                width: 82%;
                left: 63%;
                top: 50%;
                background: #E6E6E9;
                z-index: -1;
                @media #{$xs} {
                    display: none;
                }
                @media #{$md} {
                    display: none;
                }
            }
            &:hover {
                h5 {
                    background: var(--ztc-bg-bg-12);
                    color: var(--ztc-text-text-1);
                    transition: all .4s;
                }
                .icons {
                    background: var(--ztc-bg-bg-12);
                    transition: all .4s;
                    img {
                        transform: rotateY(-180deg);
                        transition: all .4s;
                        filter: brightness(0) invert(1);
                    }
                }
                
            }
            h5 {
                color: var(--ztc-text-text-13);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                font-style: normal;
                font-weight: var(--ztc-weight-medium);
                line-height: 16px; 
                display: inline-block;
                transition: all .4s;
                border-radius: 80px;
                background: rgba(109, 75, 251, 0.10);  
                padding: 8px 14px;                      
            }
            .icons {
                height: 80px;
                width: 80px;
                text-align: center;
                line-height: 80px;
                border-radius: 80px;
                background: rgba(109, 75, 251, 0.20);
                display: inline-block;
                transition: all .4s;
                img {
                    transition: all .4s;
                }         
            }
            a {
                color: var(--ztc-text-text-14);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s24);
                font-style: normal;
                font-weight: var(--ztc-weight-semibold);
                line-height: 24px; 
                display: inline-block;
                transition: all .4s;
                &:hover {
                    color: var(--ztc-text-text-13);
                    transition: all .4s;
                }
                @media #{$xs} {
                    font-size: var(--ztc-font-size-font-s18);
                }
            }
        }
    }
}
/*============= WORK CSS AREA ===============*/