"use client";

import { useRef } from 'react';
import bg1 from '@/assets/img/all-images/bg/service-bg1.png';
import blog10 from '@/assets/img/all-images/blog/blog-img10.png';
import blog8 from '@/assets/img/all-images/blog/blog-img8.png';
import blog9 from '@/assets/img/all-images/blog/blog-img9.png';
import calender3 from '@/assets/img/icons/calender3.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import {  FaAngleLeft, FaAngleRight,FaArrowRight } from 'react-icons/fa6';
import Slider from 'react-slick';
const blog = [{
  image: blog8,
  date: '4 August 2024',
  title: 'Scalable IT solutions tailored to meet a unique business needs'
}, {
  image: blog9,
  date: '5 August 2024',
  title: 'Unlock the full potential Best your business with tailored IT'
}, {
  image: blog10,
  date: '6 August 2024',
  title: 'Optimize your operations with tailored IT services that grow'
}];
const Blog = () => {
  const sliderRef = useRef(null);
  const settings = {
    dots: false,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 2000,
    arrows: false,
    speed: 500,
    slidesToShow: 3,
    pauseOnHover: true,
    slidesToScroll: 1,
    responsive: [{
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }]
  };
  return <>
    <div className="vl-blog-bottom-area sp2" style={{
      backgroundImage: `url(${bg1.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
      <Container>
        <Row>
          <Col lg={5} className="m-auto">
            <div className="heading2 space-margin60 text-center">
              <h2>View More Our Blog</h2>
            </div>
          </Col>
        </Row>
        <div className="case-slider-widget blogs-slider owl-carousel">
          <Slider ref={sliderRef} {...settings}>
            {blog.map((item, idx) => <div key={idx}>
              <div className="vl-blog-1-item">
                <div className="vl-blog-1-thumb image-anime">
                  <Image src={item.image} alt="" />
                </div>
                <div className="vl-blog-1-content">
                  <div className="vl-blog-meta">
                    <ul className='p-0 m-0'>
                      <li>
                        <Link href="">
                          <Image src={calender3} alt="" />
                          {item.date}
                        </Link>
                      </li>
                    </ul>
                  </div>
                  <div className="space16" />
                  <h4 className="vl-blog-1-title">
                    <Link href="/blog-single">{item.title}</Link>
                  </h4>
                  <div className="space20" />
                  <Link href="/blog-single" className="readmore">
                    Learn More <FaArrowRight />
                  </Link>
                </div>
              </div>
            </div>)}
          </Slider>
          <div className="owl-nav">
            <button className="owl-prev" onClick={() => sliderRef.current?.slickPrev()}>
              <FaAngleLeft className="fa-solid" />
            </button>
            <button className="owl-next" onClick={() => sliderRef.current?.slickNext()}>
              <FaAngleRight className="fa-solid" />
            </button>
          </div>
        </div>
      </Container>
    </div>
  </>;
};
export default Blog;