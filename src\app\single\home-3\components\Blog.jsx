import otherimg1 from '@/assets/img/all-images/others/others-img1.png';
import element29 from '@/assets/img/elements/elements29.png';
import logo3 from '@/assets/img/icons/sub-logo3.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
const Blog = () => {
  return <>
      <div className="others3-section-area sp1" id="support">
        <Image src={element29} alt="" className="elements29" />
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <div className="others-heading heading4">
                <h5 data-aos="fade-left" data-aos-duration={800}>
                  <Image src={logo3} alt="" />
                  All Chanel support available
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">24/7 All Time Support Any Chanel With On Platform</h2>
                <div className="space18" />
                <p data-aos="fade-left" data-aos-duration={900}>
                  We understand that encountering problems can be frustrating. <br className="d-lg-block d-none" /> That’s why our Help Desk is
                  designed to provide you with quick <br className="d-lg-block d-none" /> and effective solutions. From troubleshooting common issues.
                </p>
                <div className="space32" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1000}>
                  <Link href="/contact" className="vl-btn4">
                    Connect with Support
                  </Link>
                </div>
              </div>
            </Col>
            <Col lg={1} />
            <Col lg={5}>
              <div className="img1">
                <Image src={otherimg1} alt="" />
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Blog;