import logo3 from '@/assets/img/icons/sub-logo3.svg';
import icon1 from '@/assets/img/icons/work-icon1.svg';
import icon2 from '@/assets/img/icons/work-icon2.svg';
import icon3 from '@/assets/img/icons/work-icon3.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
export const service = [{
  image: icon1,
  title: 'Start Your HelpDesk',
  description: "Provide brief overview the problem you're facing. The more details you give, the better we can assist you."
}, {
  image: icon2,
  class: 'space30 d-lg-block d-none',
  title: 'Connect With Our Team',
  description: 'Reach out using your chosen on method. Our experts are ready to assist you provide the support.'
}, {
  image: icon3,
  class: 'space60 d-lg-block d-none',
  title: 'Receive A Solution',
  description: "Get a clear, actionable solution to your issue. We'll ensure you on to understand an next steps provide."
}];
const Service = () => {
  return <>
      <div className="works3-section-area sp2">
        <Container>
          <Row>
            <Col lg={7} className="m-auto">
              <div className="work-header text-center space-margin60 heading5">
                <h5>
                  <Image src={logo3} alt="" />
                  How It Works
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">Starts With Web Circle Technology  Simple Steps!</h2>
              </div>
            </Col>
          </Row>
          <Row>
            {service.map((item, idx) => <Col lg={4} md={6} key={idx} data-aos="zoom-in" data-aos-duration={800 + idx * 200}>
                {item.class && <div className={item.class} />}
                <div className="works-single-boxarea">
                  <div className="icons">
                    <Image src={item.image} alt="" />
                  </div>
                  <div className="space20" />
                  <div className="content-area">
                    <Link href="/service-single">{item.title}</Link>
                    <div className="space16" />
                    <p>{item.description}</p>
                  </div>
                </div>
              </Col>)}
          </Row>
        </Container>
      </div>
    </>;
};
export default Service;