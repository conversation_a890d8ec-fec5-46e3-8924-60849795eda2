import bg1 from '@/assets/img/all-images/bg/service-bg1.png';
import icon1 from '@/assets/img/icons/service-icon1.svg';
import icon2 from '@/assets/img/icons/service-icon2.svg';
import icon3 from '@/assets/img/icons/service-icon3.svg';
import icon4 from '@/assets/img/icons/service-icon4.svg';
import icon5 from '@/assets/img/icons/service-icon5.svg';
import icon6 from '@/assets/img/icons/service-icon6.svg';
import sublogo from '@/assets/img/icons/sub-logo1.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
export const business = [{
  image: icon1,
  title: 'Cyber Security Solution',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.',
  number: 1
}, {
  image: icon2,
  title: 'Scalable Cloud Solutions',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.',
  number: 2
}, {
  image: icon3,
  title: 'Data Protection Services',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.',
  number: 3
}, {
  image: icon4,
  title: 'Optimization Management',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.',
  number: 4
}, {
  image: icon5,
  title: 'HelpDesk 360 Solutions',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.',
  number: 5
}, {
  image: icon6,
  title: 'Software Development',
  description: 'Our advanced cybersecurity solution designed to protect against ever- an evolving threats, approach ensures.',
  number: 6
}];
const Serviece = () => {
  return <>
      <div className="service1-section-area sp2" id="service" style={{
      backgroundImage: `url(${bg1.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Container>
          <Row>
            <Col lg={6} className="m-auto">
              <div className="service-header text-center heading2 space-margin60">
                <h5>
                  <span>
                    <Image src={sublogo} alt="" />
                  </span>
                  Our Services
                </h5>
                <div className="space24" />
                <h2 className="text-anime-style-3">
                  Expert IT Services Designed <br className="d-lg-block d-none" /> To Elevate Your Business
                </h2>
              </div>
            </Col>
          </Row>
          <Row>
            {business.map((item, idx) => <Col lg={4} md={6} key={idx} data-aos="zoom-in" data-aos-duration={800}>
                <div className="service1-boxarea">
                  <div className="icons">
                    <Image src={item.image} alt="" />
                  </div>
                  <div className="arrow">
                    <Link href="/service-single">
                      <FaArrowRight className="fa-solid" />
                    </Link>
                  </div>
                  <div className="space24" />
                  <Link href="/service-single">{item.title}</Link>
                  <div className="space16" />
                  <p>{item.description}</p>
                  <div className="space24" />
                  <h5>0{item.number}</h5>
                </div>
              </Col>)}
          </Row>
        </Container>
      </div>
    </>;
};
export default Serviece;