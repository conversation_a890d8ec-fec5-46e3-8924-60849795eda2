@use '../utils' as *;

/*============= SERVICE CSS AREA ===============*/
.service1-section-area {
  position: relative;
  z-index: 1;
  .service-header {
    h5 {
      &::after {
        border-radius: 8px;
        background: linear-gradient(0deg, rgba(114, 110, 252, 0.1) 0%, rgba(114, 110, 252, 0.1) 100%);
        backdrop-filter: blur(5px);
      }
    }
  }

  .service1-boxarea {
    position: relative;
    z-index: 1;
    border-radius: 16px;
    background: var(--ztc-bg-bg-1);
    padding: 28px;
    transition: all 0.4s;
    margin-bottom: 30px;
    overflow: hidden;
    box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.09);
    &:hover {
      .arrow {
        top: 16px;
        right: 16px;
        transition: all 0.6s;
      }
      .icons {
        transform: rotateY(-180deg);
        transition: all 0.4s;
      }
      a {
        color: var(--ztc-text-text-1);
        transition: all 0.4s;
      }
      p {
        color: var(--ztc-text-text-1);
        transition: all 0.4s;
        opacity: 80%;
      }
      h5 {
        color: var(--ztc-text-text-1);
        transition: all 0.4s;
        padding-left: 0;
        &::after {
          background: var(--ztc-bg-bg-1);
          transition: all 0.4s;
          left: 26px;
        }
      }
      &::after {
        visibility: visible;
        opacity: 1;
        transition: all 0.4s;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
      }
    }
    &::after {
      position: absolute;
      content: '';
      height: 100%;
      width: 10px;
      transition: all 0.4s;
      left: 50%;
      border-radius: 16px;
      background: var(--Main-Color, linear-gradient(90deg, #2e0797 0%, #726efc 100%));
      visibility: hidden;
      opacity: 0;
      top: 0;
      z-index: -1;
    }
    .icons {
      height: 70px;
      width: 70px;
      text-align: center;
      line-height: 70px;
      border-radius: 50%;
      transition: all 0.4s;
      display: inline-block;
      background: var(--Main-Color, linear-gradient(90deg, #2e0797 0%, #726efc 100%));
    }
    .arrow {
      position: absolute;
      right: -100px;
      top: -100px;
      transition: all 0.8s;
      a {
        height: 40px;
        width: 40px;
        text-align: center;
        line-height: 50%;
        transition: all 0.4s;
        display: inline-block;
        background: var(--ztc-bg-bg-1);
        color: #2e0797;
        line-height: 40px;
        border-radius: 50%;
        transform: rotate(-45deg);
      }
    }
    a {
      color: var(--ztc-text-text-2);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s22);
      font-style: normal;
      font-weight: var(--ztc-weight-semibold);
      line-height: 22px;
      display: block;
      transition: all 0.4s;
    }
    p {
      color: var(--Paragraph-Color, #37385c);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s18);
      font-style: normal;
      font-weight: var(--ztc-weight-medium);
      line-height: 26px;
      transition: all 0.4s;
    }
    h5 {
      color: #2e0797;
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s16);
      font-style: normal;
      font-weight: var(--ztc-weight-semibold);
      line-height: 16px;
      padding-left: 68px;
      position: relative;
      z-index: 1;
      transition: all 0.4s;
      &::after {
        position: absolute;
        z-index: 1;
        content: '';
        height: 2px;
        width: 60px;
        left: 0;
        top: 6px;
        transition: all 0.4s;
        background: var(--Main-Color, linear-gradient(90deg, #2e0797 0%, #726efc 100%));
      }
    }
  }
}

// Homepage 02 //
.service2-section-area {
  position: relative;
  z-index: 1;

  .service2-section-bg1 {
      position: absolute;
      height: 100%;
      width: 100%;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      left: 0;
      top: 0;
      z-index: -1;
      opacity: 0.5;
  }

  .service2-section-bg2 {
        position: absolute;
        height: 100%;
        width: 100%;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        left: 0;
        top: 0;
        z-index: -2;
        opacity: 10%;
        background-attachment: fixed;
    }

  .service-widgets-section {
    position: relative;
    z-index: 1;
    overflow: hidden;
    .tab-content {
      .tab-pane {
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.06);
        backdrop-filter: blur(15px);
        padding: 40px 50px;
        transform: rotateX(45deg) translateY(50px);
        transition: all 0.4s;
        opacity: 0;
        overflow: hidden;
        @media #{$xs} {
          padding: 24px;
        }
        &.fade.show.active {
          transform: rotateX(0deg) translateY(0);
          opacity: 1;
        }
        .service-boxarea {
          .icons {
            background: rgba(192, 240, 55, 0.1);
            border-width: 1.5px;
            border: rgba(192, 240, 55, 0.1);
            display: inline-block;
            transition: all 0.4s;
            border-radius: 50%;
            height: 90px;
            width: 90px;
            text-align: center;
            line-height: 90px;
            img {
              height: 50px;
              width: 50px;
              object-fit: contain;
            }
          }
          .content-area {
            h3 {
              color: var(--ztc-text-text-1);
              font-family: var(--ztc-family-font1);
              font-size: var(--ztc-font-size-font-s28);
              font-style: normal;
              font-weight: var(--ztc-weight-semibold);
              line-height: 28px;
              display: inline-block;
            }
            p {
              color: rgba(255, 255, 255, 0.8);
              font-family: var(--ztc-family-font1);
              font-size: var(--ztc-font-size-font-s18);
              font-style: normal;
              font-weight: var(--ztc-weight-medium);
              line-height: 26px;
            }
          }
        }
        .images-area {
          position: relative;
          @media #{$xs} {
            margin-top: 30px;
          }

          @media #{$md} {
            margin-top: 30px;
          }
          .img1 {
            img {
              height: 100%;
              width: 100%;
              object-fit: cover;
              border-radius: 200px 200px 0 0;
            }
          }
          .arrow-circle {
            a {
              height: 160px;
              width: 160px;
              display: inline-block;
              transition: all 0.4s;
              border-radius: 50%;
              background: var(--ztc-bg-bg-6);
              position: absolute;
              bottom: -20px;
              left: -30px;
              z-index: 1;
              .arrow1 {
                position: absolute;
                top: 41%;
                left: 44%;
              }
              .elements20 {
                position: absolute;
                top: 6px;
                left: 6px;
              }
            }
          }
        }
      }
    }
    .tabs-btn-area {
      position: relative;
      z-index: 1;
      &::after {
        position: absolute;
        content: '';
        height: 10px;
        width: 100%;
        background: #203b44;
        top: -32px;
        left: 0;
        right: 0;
        transition: all 0.4s;
      }
      ul {
        justify-content: space-between;
        align-items: center;
        @media #{$xs} {
          justify-content: center;
        }
        li {
          &:nth-child(4) {
            @media #{$md} {
              margin-top: 20px;
            }
          }
          button {
            border-radius: 120px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: #10303a;
            padding: 8px 16px 8px 8px;
            position: relative;
            z-index: 1;
            color: var(--ztc-text-text-1);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 18px;
            @media #{$xs} {
              display: block !important;
              margin-bottom: 16px;
            }
            &::after {
              position: absolute;
              z-index: 1;
              content: '';
              height: 10px;
              left: 0;
              top: -32px;
              transition: all 0.4s;
              width: 100%;
              background: var(--ztc-bg-bg-6);
              border-radius: 40px;
              visibility: hidden;
              opacity: 0;
              @media #{$xs} {
                display: none;
              }
            }
            &.active {
              background: var(--ztc-bg-bg-6);
              color: var(--ztc-text-text-6);
              &::after {
                visibility: visible;
                opacity: 1;
                transition: all 0.4s;
              }
              span {
                background: rgba(3, 37, 48, 0.2);
                img {
                  transition: all 0.4s;
                  filter: brightness(0);
                }
              }
            }
            span {
              height: 48px;
              width: 48px;
              text-align: center;
              line-height: 48px;
              border-radius: 50%;
              transition: all 0.4s;
              display: inline-block;
              background: rgba(192, 240, 55, 0.06);
              border-width: 1px;
              border: rgba(192, 240, 55, 0.1);
              margin: 0 8px 0 0;
              img {
                height: 28px;
                width: 28px;
                object-fit: contain;
                transition: all 0.4s;
              }
            }
          }
        }
      }
    }
  }
}

// Homepage 04 //
.service4-section-area {
  position: relative;
  z-index: 1;
  .elements42 {
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .service-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @media #{$xs} {
      display: inline-block;
      .btn-area1 {
        margin-top: 20px;
      }
    }
    @media #{$md} {
      display: inline-block;
      .btn-area1 {
        margin-top: 20px;
      }
    }
  }
  .service-single-boxarea {
    position: relative;
    z-index: 1;
    overflow: hidden;
    transition: all 0.4s;
    border-radius: 8px;
    background: var(--ztc-bg-bg-1);
    padding: 32px;
    margin-top: 30px;
    &:hover {
      &::after {
        visibility: visible;
        opacity: 1;
        transition: all 0.4s;
        width: 100%;
        left: 0;
        top: 0;
      }
      .icons {
        background: var(--ztc-bg-bg-1);
        transition: all 0.4s;
        transform: rotateY(-180deg);
      }
      .content-area {
        a {
          color: var(--ztc-text-text-1);
          transition: all 0.4s;
        }
        p {
          color: var(--ztc-text-text-1);
          opacity: 90%;
        }
        .btn-area {
          a.service-btn {
            opacity: 1;
            color: var(--ztc-text-text-12);
            transition: all 0.4s;
            &::after {
              transition: all 0.4s;
              visibility: hidden;
              opacity: 0;
            }
            &::before {
              visibility: visible;
              opacity: 1;
              right: auto;
              left: 0;
            }
          }
        }
      }
    }
    &::after {
      position: absolute;
      content: '';
      height: 100%;
      width: 10px;
      left: 50%;
      transition: all 0.4s;
      background: var(--ztc-bg-bg-10);
      border-radius: 8px;
      top: 0;
      visibility: hidden;
      opacity: 0;
      z-index: -1;
    }
    .icons {
      height: 90px;
      width: 90px;
      text-align: center;
      line-height: 90px;
      display: inline-block;
      border-radius: 50%;
      background: #f2f4ff;
      transition: all 0.4s;
    }
    .content-area {
      a {
        color: var(--ztc-text-text-10);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s24);
        font-style: normal;
        font-weight: var(--ztc-weight-bold);
        line-height: 24px;
        display: inline-block;
        transition: all 0.4s;
        &:hover {
          color: var(--ztc-text-text-1);
          transition: all 0.4s;
          opacity: 80%;
        }
      }
      p {
        color: var(--ztc-text-text-11);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-style: normal;
        font-weight: var(--ztc-weight-medium);
        line-height: 26px;
        transition: all 0.4s;
      }
      .btn-area {
        a.service-btn {
          color: var(--ztc-text-text-1);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s18);
          font-style: normal;
          font-weight: var(--ztc-weight-bold);
          line-height: 18px;
          display: inline-block;
          transition: all 0.4s;
          padding: 14px 20px;
          border-radius: 70px;
          position: relative;
          z-index: 1;
          &::after {
            position: absolute;
            content: '';
            height: 100%;
            width: 100%;
            background: var(--ztc-bg-bg-10);
            left: 0;
            top: 0;
            z-index: -1;
            border-radius: 70px;
            transition: all 0.4s;
            visibility: visible;
            opacity: 1;
          }
          &::before {
            position: absolute;
            content: '';
            height: 100%;
            width: 100%;
            background: var(--ztc-bg-bg-1);
            right: 0;
            top: 0;
            z-index: -2;
            border-radius: 70px;
            transition: all 0.4s;
            visibility: hidden;
            opacity: 0;
          }
        }
      }
    }
  }
}

// Homepage 05 //
.service5-section-area {
  position: relative;
  z-index: 1;
  margin: 0 30px;
  padding: 100px 0 230px;
  height: 170px;
  box-sizing: border-box;
  border-radius: 16px;
  @media #{$xs} {
    margin: 0;
    padding: 50px 0 50px;
    border-radius: 0;
  }
  @media #{$md} {
    margin: 0;
    padding: 50px 0 50px;
    border-radius: 0;
  }
  .slick-slide {
    padding-right: 30px;
    box-sizing: border-box;
  }
  .service5-slider-box {
    position: relative;
    z-index: 2;
    .owl-stage-outer {
      position: absolute;
      z-index: 2;
      @media #{$xs} {
        position: relative;
      }
      @media #{$md} {
        position: relative;
      }
    }
    .owl-nav {
      position: absolute;
      right: 0;
      top: -120px;
      @media #{$xs} {
        position: relative;
        left: 0;
        top: 0;
        margin-top: 30px;
        text-align: center;
      }
      @media #{$md} {
        position: relative;
        left: 0;
        top: 0;
        margin-top: 30px;
        text-align: center;
      }
      button {
        height: 60px;
        width: 60px;
        border-radius: 50%;
        background: var(--ztc-bg-bg-1);
        display: inline-block;
        transition: all 0.4s;
        color: var(--ztc-text-text-13);
        text-align: center;
        font-size: var(--ztc-font-size-font-s20);
        border: transparent;
        &:hover {
          background: var(--ztc-bg-bg-12);
          color: var(--ztc-text-text-1);
          transition: all 0.4s;
        }
        &.owl-prev {
          margin: 0 16px 0 0;
        }
      }
    }
    .service-slider-box {
      position: relative;
      z-index: 1;
      border-radius: 16px;
      background: var(--ztc-bg-bg-1);
      padding: 32px 44px;
      text-align: center;
      border: 1px solid rgba(189, 189, 189, 0.12);
      @media #{$md} {
        padding: 32px;
      }
      .icons {
        height: 80px;
        width: 80px;
        text-align: center;
        line-height: 80px;
        border-radius: 50%;
        transition: all 0.4s;
        background: var(--ztc-bg-bg-12);
        display: inline-block;
        margin: 0 auto;
        img {
          height: 40px;
          width: 40px;
          display: inline-block;
          object-fit: contain;
        }
      }
      .content-area {
        a {
          color: var(--ztc-text-text-14);
          text-align: center;
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s24);
          font-style: normal;
          font-weight: var(--ztc-weight-semibold);
          line-height: 24px;
          display: inline-block;
          transition: all 0.4s;
        }
        p {
          color: var(--ztc-text-text-15);
          text-align: center;
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 26px;
        }
      }
    }
  }
}

// Inner Pages Area //
.service-sidebar-area {
  position: relative;
  z-index: 1;
  .service-widget-sidebar {
    position: sticky;
    top: 100px;
    h3 {
      color: var(--ztc-text-text-2);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s24);
      font-style: normal;
      font-weight: var(--ztc-weight-semibold);
      line-height: 24px;
    }
    .search-area {
      position: relative;
      z-index: 1;
      border-radius: 8px;
      background: var(--Gray-Color, #eff1ff);
      padding: 24px 28px;
      form {
        position: relative;
        z-index: 1;
        input {
          width: 100%;
          border-radius: 8px;
          background: var(--ztc-bg-bg-1);
          color: var(--Text-Color, #050734);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 16px;
          padding: 19px 16px;
        }
        button {
          border: none;
          background: none;
          outline: none;
          position: absolute;
          right: 12px;
          top: 12px;
          font-size: var(--ztc-font-size-font-s22);
        }
      }
    }
    .category-list-area {
      border-radius: 8px;
      background: var(--Gray-Color, #eff1ff);
      padding: 24px 28px;
      ul {
        li {
          margin-top: 18px;
          a {
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: var(--Text-Color, #050734);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 18px;
            border-radius: 8px;
            background: var(--ztc-bg-bg-1);
            padding: 20px;
            transition: all 0.4s;
            position: relative;
            z-index: 1;
            &:hover {
              transition: all 0.4s;
              color: var(--ztc-text-text-1);
              &::after {
                visibility: visible;
                opacity: 1;
                transition: all 0.4s;
              }
            }
            &::after {
              position: absolute;
              content: '';
              width: 100%;
              height: 100%;
              left: 0;
              top: 0;
              background: var(--ztc-bg-bg-5);
              transition: all 0.4s;
              border-radius: 8px;
              z-index: -1;
              visibility: hidden;
              opacity: 0;
            }
          }
        }
      }
    }
    .tags-area {
      border-radius: 8px;
      background: var(--Gray-Color, #eff1ff);
      padding: 24px 28px;
      ul {
        li {
          display: inline-block;
          a {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            line-height: 18px;
            display: inline-block;
            border-radius: 4px;
            background: var(--ztc-bg-bg-1);
            padding: 10px;
            font-weight: var(--ztc-weight-semibold);
            transition: all 0.4s;
            color: var(--ztc-text-text-2);
            margin-top: 16px;
            position: relative;
            z-index: 1;
            margin-right: 12px;
            @media #{$xs} {
              margin-right: 0;
            }
            &:hover {
              transition: all 0.4s;
              color: var(--ztc-text-text-1);
              &::after {
                visibility: visible;
                opacity: 1;
                transition: all 0.4s;
              }
            }
            &::after {
              position: absolute;
              content: '';
              width: 100%;
              height: 100%;
              left: 0;
              top: 0;
              background: var(--ztc-bg-bg-5);
              transition: all 0.4s;
              border-radius: 8px;
              z-index: -1;
              visibility: hidden;
              opacity: 0;
            }
          }
        }
      }
    }
    .contact-boxarea {
      border-radius: 8px;
      background: var(--Gray-Color, #eff1ff);
      padding: 24px 28px;
      .input-area {
        margin-top: 16px;
        input {
          width: 100%;
          color: var(--ztc-text-text-2);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 16px;
          padding: 20px;
          border-radius: 4px;
          background: var(--ztc-bg-bg-1);
          &::placeholder {
            color: var(--ztc-text-text-3);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 16px;
          }
        }

        textarea {
          width: 100%;
          color: var(--ztc-text-text-2);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 16px;
          padding: 20px;
          border-radius: 4px;
          background: var(--ztc-bg-bg-1);
          height: 120px;
          &::placeholder {
            color: var(--ztc-text-text-3);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s16);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 16px;
          }
        }
        button {
          border: none;
          outline: none;
        }
      }
    }
  }
  .service2-widget-sidebar.rightside {
    padding: 0 50px 0 0;
    @media #{$xs} {
      padding: 0;
      margin-bottom: 50px;
    }
    @media #{$md} {
      padding: 0;
      margin-bottom: 50px;
    }
  }
  .service2-widget-sidebar {
    padding: 0 0 0 50px;
    @media #{$xs} {
      padding: 0;
      margin-top: 50px;
    }
    @media #{$md} {
      padding: 0;
      margin-top: 50px;
    }
    h3 {
      color: var(--ztc-text-text-2);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s32);
      font-style: normal;
      font-weight: var(--ztc-weight-semibold);
      line-height: 32px;
    }
    p {
      color: var(--ztc-text-text-3);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s18);
      font-style: normal;
      font-weight: var(--ztc-weight-medium);
      line-height: 26px;
      letter-spacing: -0.18px;
    }
    .list {
      li {
        margin-top: 18px;
        img {
          height: 20px;
          width: 20px;
          object-fit: cover;
          border-radius: 50%;
          margin: 0 4px 0 0;
        }
        color: var(--ztc-text-text-2);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-style: normal;
        font-weight: var(--ztc-weight-medium);
        line-height: 18px;
        opacity: 90%;
      }
    }
    .pera-box {
      border-radius: 8px;
      background: var(--ztc-bg-bg-1);
      box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
      padding: 22px 22px 22px 28px;
      position: relative;
      z-index: 1;
      &::after {
        position: absolute;
        content: '';
        height: 100%;
        width: 6px;
        left: 0;
        top: 0;
        border-radius: 8px 0px 0px 8px;
        background: var(--Main-Color, linear-gradient(90deg, #2e0797 0%, #726efc 100%));
      }
      h4 {
        color: var(--Text-Color, #050734);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s24);
        font-style: normal;
        font-weight: var(--ztc-weight-semibold);
        line-height: 24px;
      }
    }
    .faq-widget-area {
      .accordion {
        .accordion-item {
          border-radius: 8px;
          background: var(--Main-Color, linear-gradient(90deg, #2e0797 0%, #726efc 100%));
          border: none;
          border-radius: 8px;
          button {
            box-shadow: none;
            outline: none;
            border: none;
            padding: 26px 24px;
            color: var(--ztc-text-text-2);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s24);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 24px;
            text-transform: capitalize;
            border-radius: 6px;
            background: var(--Gray-Color, #eff1ff);
            @media #{$xs} {
              font-size: var(--ztc-font-size-font-s20);
              line-height: 28px;
            }
            &.accordion-button:not(.collapsed) {
              background: none;
              color: var(--ztc-text-text-1);
              padding: 26px 24px 22px;
              &::after {
                z-index: 1;
                filter: brightness(0);
              }
              &::before {
                background: var(--ztc-bg-bg-1);
              }
            }
            &::after {
              z-index: 1;
              filter: brightness(0) invert(1);
            }
            &::before {
              position: absolute;
              content: '';
              height: 28px;
              width: 28px;
              border-radius: 50%;
              background: #6d4bfb;
              right: 20px;
              top: 23px;
              @media #{$xs} {
                top: 40px;
              }
            }
          }
          .accordion-body {
            padding: 0;
            p {
              color: var(--ztc-text-text-1);
              opacity: 90%;
              padding: 0 24px 24px 30px;
            }
          }
        }
      }
    }
  }
}
/*============= SERVICE CSS AREA ENDS===============*/
