'use client';

import logo3 from '@/assets/img/logo/logo3.png';
import MobileMenu from '@/components/layout/TopBar/components/MobileMenu';
import TopMenu from '@/components/layout/TopBar/components/TopMenu';
import useScrollEvent from '@/hook/useScrollEvent';
import useToggle from '@/hook/useToggle';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaArrowRight, FaBarsStaggered } from 'react-icons/fa6';
const Header = () => {
  const {
    scrollY
  } = useScrollEvent();
  const {
    isOpen: openMobilMenu,
    toggle: toggleMobilMenu
  } = useToggle();
  return <>
      <header className="homepage2-body">
        <div id="vl-header-sticky" className={`vl-header-area vl-transparent-header header-${scrollY > 100 && 'sticky'}`}>
          <Container className="headerfix">
            <Row className="align-items-center row-bg2">
              <Col lg={2} md={6} xs={6}>
                <div className="vl-logo">
                  <Link href="/">
                    <Image src={logo3} alt="" />
                  </Link>
                </div>
              </Col>
              <Col lg={7} className="d-none d-lg-block">
                <div className="vl-main-menu text-center">
                  <nav className="vl-mobile-menu-active">
                    <TopMenu classname="btn-area1" button="vl-btn3" />
                  </nav>
                </div>
              </Col>
              <Col lg={3} md={6} xs={6}>
                <div className="vl-hero-btn d-none d-lg-block text-end">
                  <span className="vl-btn-wrap text-end">
                    <Link href="/contact" className="vl-btn3">
                      <span className="demo">Get In Touch</span>
                      <span className="arrow">
                        <FaArrowRight className="fa-solid" />
                      </span>
                    </Link>
                  </span>
                </div>
                <div className="vl-header-action-item d-block d-lg-none">
                  <button onClick={toggleMobilMenu} type="button" className="vl-offcanvas-toggle">
                    <FaBarsStaggered className="fa-solid" />
                  </button>
                </div>
              </Col>
            </Row>
          </Container>
        </div>
        <MobileMenu toggleMobilMenu={toggleMobilMenu} openMobilMenu={openMobilMenu} />
      </header>
    </>;
};
export default Header;