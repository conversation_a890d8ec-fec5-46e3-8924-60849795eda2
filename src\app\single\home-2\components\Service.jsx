'use client';

import bg1 from '@/assets/img/all-images/bg/service-bg2.png';
import bg2 from '@/assets/img/all-images/bg/team-bg1.png';
import ser1 from '@/assets/img/all-images/service/service-img1.png';
import element20 from '@/assets/img/elements/elements20.png';
import arrow1 from '@/assets/img/icons/arrow1.svg';
import icon10 from '@/assets/img/icons/service-icon10.svg';
import icon7 from '@/assets/img/icons/service-icon7.svg';
import icon8 from '@/assets/img/icons/service-icon8.svg';
import icon9 from '@/assets/img/icons/service-icon9.svg';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Nav, NavItem, NavLink, Row, TabContainer, Tab<PERSON>ontent, TabPane } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
export const work = [{
  id: '1',
  image: icon7,
  title: '24/7 Call Handling',
  name: 'pills-home',
  class: 'pills-home-tab'
}, {
  id: '2',
  image: icon8,
  title: 'Live Chat Support',
  name: 'pills-profile',
  class: 'pills-profile-tab'
}, {
  id: '3',
  image: icon9,
  title: 'Lead Generation',
  name: 'pills-contact',
  class: 'pills-contact-tab'
}, {
  id: '4',
  image: icon10,
  title: 'Professional Staff',
  name: 'pills-contact1',
  class: 'pills-contact1-tab'
}];
const Service = () => {
  return <>
      <div className="service2-section-area sp1" id="service">
        <div className="service2-section-bg1" style={{
        backgroundImage: `url(${bg1.src})`
      }}></div>
        <div className="service2-section-bg2" style={{
        backgroundImage: `url(${bg2.src})`
      }}></div>
        <Container>
          <TabContainer defaultActiveKey="24/7 Call Handling">
            <Row>
              <Col lg={6} className="m-auto">
                <div className="service-header text-center heading4 space-margin60">
                  <h5>
                    <Image src={logo2} alt="" />
                    Our Services
                  </h5>
                  <div className="space20" />
                  <h2 className="text-anime-style-3">
                    We Have The Best Experts <br className="d-lg-block d-none" /> To Elevate Your Business
                  </h2>
                </div>
              </Col>
            </Row>
            <Row>
              <Col lg={10} className="m-auto">
                <div className="service-widgets-section">
                  <TabContent className="tab-content" id="pills-tabContent">
                    {(work || []).map((item, idx) => {
                    return <TabPane eventKey={item.title} key={idx} className="fade" id={item.id} role="tabpanel" aria-labelledby={item.class} tabIndex={0}>
                          <Row className="align-items-center">
                            <Col lg={6}>
                              <div className="service-boxarea">
                                <div className="icons" data-aos="fade-left" data-aos-duration={700}>
                                  <Image src={item.image} alt="" />
                                </div>
                                <div className="space28" />
                                <div className="content-area">
                                  <h3 data-aos="fade-left" data-aos-duration={800}>
                                    {item.title}
                                  </h3>
                                  <div className="space16" />
                                  <p data-aos="fade-left" data-aos-duration={900}>
                                    We provide round-the-clock support for all your inbound and outbound call needs, ensuring that no customer query
                                    going on the unanswered.
                                  </p>
                                  <div className="space28" />
                                  <div className="btn-area" data-aos="fade-left" data-aos-duration={1000}>
                                    <Link href="/service" className="vl-btn3">
                                      <span className="demo">Learn More</span>
                                      <span className="arrow">
                                        <FaArrowRight />
                                      </span>
                                    </Link>
                                  </div>
                                </div>
                              </div>
                            </Col>
                            <Col lg={1} />
                            <Col lg={5}>
                              <div className="images-area">
                                <div className="img1 ">
                                  <Image src={ser1} alt="" />
                                </div>
                                <div className="arrow-circle text-end">
                                  <Link href="">
                                    <Image src={element20} alt="" className="elements20 keyframe5" />
                                    <Image src={arrow1} alt="" className="arrow1" />
                                  </Link>
                                </div>
                              </div>
                            </Col>
                          </Row>
                        </TabPane>;
                  })}
                  </TabContent>
                  <div className="space80" />
                  <div className="tabs-btn-area">
                    <Nav as={'ul'} className="nav-pills" id="pills-tab" role="tablist">
                      {(work || []).map((item, idx) => {
                      return <NavItem key={idx} as={'li'} role="presentation">
                            <NavLink as={'button'} eventKey={item.title} id="pills-home-tab" data-bs-toggle="pill" data-bs-target={`#${item.name}`} type="button" role="tab" aria-controls={item.name} aria-selected="true">
                              <span>
                                <Image src={item.image} alt="" />
                              </span>
                              {item.title}
                            </NavLink>
                          </NavItem>;
                    })}
                    </Nav>
                  </div>
                </div>
              </Col>
            </Row>
          </TabContainer>
        </Container>
      </div>
    </>;
};
export default Service;