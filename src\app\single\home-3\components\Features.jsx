import blog6 from '@/assets/img/all-images/blog/blog-img6.png';
import blog7 from '@/assets/img/all-images/blog/blog-img7.png';
import elements28 from '@/assets/img/elements/elements28.png';
import logo3 from '@/assets/img/icons/sub-logo3.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
const Features = () => {
  return <>
      <div className="features3-section-area sp1" id="features">
        <Container>
          <Row>
            <Col lg={6} className="m-auto">
              <div className="features heading heading5 text-center">
                <h5>
                  <Image src={logo3} alt="" />
                  Web Circle Technology  all features
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">Discover Why Web Circle Technology  Unique</h2>
              </div>
            </Col>
            <div className="space100 d-lg-block d-none" />
            <div className="space60 d-lg-none d-block" />
          </Row>
          <Row className="align-items-center">
            <Col lg={6} data-aos="zoom-in" data-aos-duration={1000}>
              <div className="images-area">
                <Image src={elements28} alt="" className="elements28" />
                <svg xmlns="http://www.w3.org/2000/svg" width={483} height={467} viewBox="0 0 483 467" fill="none">
                  <path fillRule="evenodd" clipRule="evenodd" d="M266.577 463.258C200.182 477.787 129.972 448.449 80.196 402.148C28.7424 354.286 2.6788 285.923 0.403656 215.667C-1.9523 142.916 8.49039 58.9734 67.7138 16.6983C123.868 -23.3859 197.78 20.6378 266.577 25.6664C326.276 30.0299 393.811 3.79349 438.363 43.7902C483.846 84.6228 489.019 155.489 478.219 215.667C468.978 267.16 420.048 295.457 386.062 335.216C346.629 381.348 325.846 450.289 266.577 463.258Z" fill="#D8CEED" fillOpacity="0.7" />
                </svg>
                <div className="img1 image-anime">
                  <Image src={blog6} alt="" height={429} className='img-fluid' />
                </div>
              </div>
            </Col>
            <Col lg={5}>
              <div className="content-area heading5">
                <h3 className="text-anime-style-3">Resolve Your Issues Quickly with Our Best On Help Desk</h3>
                <div className="space18" />
                <p data-aos="fade-left" data-aos-duration={900}>
                  Our Help Desk is dedicated to providing you with the support you need, exactly when you need it. Whether you're troubleshooting a
                  technical issue, looking for .
                </p>
                <div className="space18" />
                <p data-aos="fade-left" data-aos-duration={1000}>
                  Experience seamless support with our Help Desk, The designed to address your needs swiftly an efficiently.
                </p>
                <div className="space28" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1200}>
                  <Link href="/contact" className="vl-btn4">
                    Contact Support
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
          <div className="space100 d-lg-block d-none" />
          <div className="space60 d-lg-block d-none" />
          <Row className="align-items-center">
            <Col lg={5}>
              <div className="content-area2 heading5">
                <h3 className="text-anime-style-3">We’re Here to Help-24/7 All Support at Your Fingertips</h3>
                <div className="space18" />
                <p data-aos="fade-left" data-aos-duration={900}>
                  Our Help Desk is designed with you mind, providing fast and effective solutions to any issues you may encounter. With support
                  available around the clock.
                </p>
                <div className="space18" />
                <p data-aos="fade-left" data-aos-duration={1000}>
                  we are committed to delivering top-notch support tailored to your needs. Our knowledgeable team is.
                </p>
                <div className="space28" />
                <div className="btn-area1" data-aos="fade-left" data-aos-duration={1200}>
                  <Link href="/contact" className="vl-btn4">
                    Contact Support
                  </Link>
                </div>
              </div>
              <div className="space50 d-lg-none d-block" />
            </Col>
            <Col lg={1} />
            <Col lg={6} data-aos="zoom-in" data-aos-duration={1100}>
              <div className="images-area">
                <Image src={elements28} alt="" className="elements28" />
                <svg xmlns="http://www.w3.org/2000/svg" width={483} height={467} viewBox="0 0 483 467" fill="none">
                  <path fillRule="evenodd" clipRule="evenodd" d="M266.577 463.258C200.182 477.787 129.972 448.449 80.196 402.148C28.7424 354.286 2.6788 285.923 0.403656 215.667C-1.9523 142.916 8.49039 58.9734 67.7138 16.6983C123.868 -23.3859 197.78 20.6378 266.577 25.6664C326.276 30.0299 393.811 3.79349 438.363 43.7902C483.846 84.6228 489.019 155.489 478.219 215.667C468.978 267.16 420.048 295.457 386.062 335.216C346.629 381.348 325.846 450.289 266.577 463.258Z" fill="#D8CEED" fillOpacity="0.7" />
                </svg>
                <div className="img1 image-anime">
                  <Image src={blog7} alt="" height={429} className='img-fluid' />
                </div>
              </div>
            </Col>
          </Row>
        </Container>
        <div className="space50 d-block d-none" />
      </div>
    </>;
};
export default Features;