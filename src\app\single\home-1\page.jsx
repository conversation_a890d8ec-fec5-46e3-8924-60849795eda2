import Footer from '@/components/layout/Footer/Footer';
import About from './components/About';
import Blog from './components/Blog';
import CTA from './components/CTA';
import Header from './components/Header';
import Hero from './components/Hero';
import Serviece from './components/Serviece';
import Solutions from './components/Solutions';
import Team from './components/Team';
import Testimonial from './components/Testimonial';
import Work from './components/Work';
import logo from '@/assets/img/logo/fav-logo1.png';
export const metadata = {
  title: 'Web Circle Technology  - Technology & It Solutions Services',
  icons: {
    icon: logo.src
  }
};
const page = () => {
  return <>
      <Header />
      <Hero />
      <div data-bs-spy="scroll" data-bs-target="#navbar-example2" data-bs-root-margin="0px 0px -40%" data-bs-smooth-scroll="true" className="scrollspy-example bg-body-tertiary rounded-2" tabIndex={0}>
        <About />
        <Serviece />
        <Solutions />
        <Work />
        <Testimonial />
        <Team />
        <Blog />
        <CTA />
        <Footer />
      </div>
    </>;
};
export default page;