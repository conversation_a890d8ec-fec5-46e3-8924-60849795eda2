'use client';

import bg1 from '@/assets/img/all-images/bg/footer-bg2.png';
import tes2 from '@/assets/img/all-images/testimonial/testimonial-img2.png';
import tes3 from '@/assets/img/all-images/testimonial/testimonial-img3.png';
import tes4 from '@/assets/img/all-images/testimonial/testimonial-img4.png';
import tes5 from '@/assets/img/all-images/testimonial/testimonial-img5.png';
import tes6 from '@/assets/img/all-images/testimonial/testimonial-img6.png';
import elements17 from '@/assets/img/elements/elements17.png';
import elements21 from '@/assets/img/elements/elements21.png';
import quito1 from '@/assets/img/icons/quito2.svg';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
import { Col, Container, Row } from 'react-bootstrap';
import { FaStar } from 'react-icons/fa6';
import Slider from 'react-slick';
export const slider = [{
  image: tes2,
  name: '<PERSON>',
  position: 'Owner Taxfirm',
  title: 'Outstanding Service And 24/7 Support'
}, {
  image: tes3,
  name: 'Ben Stokes',
  position: 'Owner Taxfirm'
}, {
  image: tes4,
  name: 'Ben Stokes',
  position: 'Owner Taxfirm'
}, {
  image: tes5,
  name: 'Ben Stokes',
  position: 'Owner Taxfirm'
}, {
  image: tes2,
  name: 'Ben Stokes',
  position: 'Owner Taxfirm'
}];
export const slider2 = [{
  image: tes2
}, {
  image: tes3
}, {
  image: tes4
}, {
  image: tes5
}, {
  image: tes2
}, {
  image: tes2
}, {
  image: tes3
}, {
  image: tes4
}, {
  image: tes5
}, {
  image: tes2
}, {
  image: tes2
}, {
  image: tes3
}, {
  image: tes4
}, {
  image: tes5
}];
const Testimonial = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [previousIndex, setPreviousIndex] = useState(0);
  const [nav1, setNav1] = useState();
  const [nav2, setNav2] = useState();
  const sliderRef1 = useRef(null);
  const sliderRef2 = useRef(null);
  useEffect(() => {
    setNav1(sliderRef1.current);
    setNav2(sliderRef2.current);
  }, []);
  const settings1 = {
    dots: false,
    infinite: true,
    arrows: false,
    slidesToShow: 4,
    autoplay: true,
    slidesToScroll: 1,
    vertical: true,
    verticalSwiping: true,
    focusOnSelect: true,
    beforeChange: (oldIndex, newIndex) => {
      setPreviousIndex(oldIndex);
      setActiveIndex(newIndex);
    }
  };
  const settings2 = {
    dots: false,
    infinite: true,
    speed: 500,
    arrows: false,
    autoplay: false,
    slidesToShow: 1,
    slidesToScroll: 1
  };
  return <>
      <div className="testimonial2-section-area sp1" id="testimonial" style={{
      backgroundImage: `url(${bg1.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Container>
          <Row>
            <Col lg={5} className="m-auto">
              <div className="testimonial-header text-center heading4 space-margin60">
                <h5>
                  <span>
                    <Image src={logo2} alt="" />
                  </span>
                  Testimonials
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">What Our Clients Say</h2>
              </div>
            </Col>
          </Row>
          <Row>
            <Col lg={5}>
              <div className="img1 image-anime">
                <Image src={tes6} alt="" />
              </div>
            </Col>
            <Col lg={6}>
              <div className="slider-galeria">
                <Slider ref={sliderRef2} asNavFor={nav1} {...settings2}>
                  {slider.map((item, idx) => <div key={idx} className="testimonial-slider-content-area">
                      <div className="testimonial-author-area">
                        <ul className="m-0 p-0 d-inline-flex gap-1">
                          <li>
                            <Link href="">
                              <FaStar />
                            </Link>
                          </li>
                          <li>
                            <Link href="">
                              <FaStar />
                            </Link>
                          </li>
                          <li>
                            <Link href="">
                              <FaStar />
                            </Link>
                          </li>
                          <li>
                            <Link href="">
                              <FaStar />
                            </Link>
                          </li>
                          <li>
                            <Link href="">
                              <FaStar />
                            </Link>
                          </li>
                        </ul>
                        <div className="space16" />
                        <Image src={quito1} alt="" className="quito2" />
                        <Image src={elements21} alt="" className="elements21" />
                        {item.title && <h4>{item.title}</h4>}
                        <div className="space12" />
                        <p>
                          “We’ve been working with Web Circle Technology  for over two years, an their 24/7 customer service has been a game-changer for us. They
                          handle our inbound calls professionally and always ensure our customers are happy their ability.”
                        </p>
                      </div>
                      <div className="space60" />
                      <div className="testimonial-man-info-area">
                        <div className="man-images-text">
                          <div className="mans-img">
                            <Image src={item.image} alt="" />
                          </div>
                          <div className="man-text">
                            <Link href="/team">{item.name}</Link>
                            <div className="space12" />
                            <p>{item.position}</p>
                          </div>
                        </div>
                        <Image src={elements17} alt="" className="elements17" />
                      </div>
                    </div>)}
                </Slider>
              </div>
            </Col>
            <Col lg={1}>
              <div className="slider-galeria-thumbs text-center d-lg-block d-none">
                <Slider ref={sliderRef1} asNavFor={nav2} {...settings1}>
                  {slider2.map((item, idx) => <div key={idx} className={`testimonial3-sliders-img ${idx === activeIndex ? 'active-thumb' : idx === previousIndex ? 'previous-thumb' : ''}`}>
                      <Image src={item.image} alt="" />
                    </div>)}
                </Slider>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Testimonial;