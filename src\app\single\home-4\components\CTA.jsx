import bg1 from '@/assets/img/all-images/bg/hero-bg5.png';
import cta3 from '@/assets/img/all-images/cta/cta-img3.png';
import elements1 from '@/assets/img/elements/elements1.png';
import elements16 from '@/assets/img/elements/elements16.png';
import elements33 from '@/assets/img/elements/elements33.png';
import element36 from '@/assets/img/elements/elements36.png';
import Image from 'next/image';
import { Col, Container, Form, Row } from 'react-bootstrap';
import { FaArrowRight } from 'react-icons/fa6';
const CTA = () => {
  return <>
      <div className="cta4-section-area sp1" style={{
      backgroundImage: `url(${bg1.src})`,
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover'
    }}>
        <Image src={elements33} alt="" className="elements33 aniamtion-key-1" />
        <Image src={elements1} alt="" className="elements1 keyframe5" />
        <Image src={elements16} alt="" className="elements16" />
        <Container>
          <Row>
            <Col lg={5}>
              <div className="cta-header heading1">
                <h2 data-aos="fade-left" data-aos-duration={700}>
                  Empower Your Business Advanced Technology
                </h2>
                <div className="space16" />
                <p data-aos="fade-left" data-aos-duration={800}>
                  Feel free to customize this paragraph to better reflect the specific services offered by your IT solution &amp; the unique
                </p>
                <div className="space24" />
                <Form data-aos="fade-left" data-aos-duration={900}>
                  <input type="text" placeholder="Email Address" />
                  <button type="submit" className="vl-btn5">
                    <span className="demo">Subscribe</span>
                    <span className="arrow">
                      <FaArrowRight />
                    </span>
                  </button>
                </Form>
              </div>
            </Col>
            <Col lg={1} />
            <Col lg={4}>
              <div className="cta-images">
                <Image src={element36} alt="" className="elements36 keyframe5 img-fluid" height={464} width={550} />
                <div className="img1">
                  <Image src={cta3} alt="" />
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default CTA;