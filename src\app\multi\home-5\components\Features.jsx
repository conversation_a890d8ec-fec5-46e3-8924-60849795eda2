import work1 from '@/assets/img/icons/work1.svg';
import work2 from '@/assets/img/icons/work2.svg';
import work3 from '@/assets/img/icons/work3.svg';
import work4 from '@/assets/img/icons/work4.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
const work = [{
  image: work1,
  title: 'User Analytics',
  number: 1
}, {
  image: work2,
  title: 'Creative Design',
  number: 2
}, {
  image: work3,
  title: 'Smart Coding',
  number: 3
}, {
  image: work4,
  title: 'Online Support',
  number: 4
}];
const Features = () => {
  return <>
      <div className="work5-section-area sp8">
        <Container>
          <Row>
            <Col lg={6} className="m-auto">
              <div className="work-header text-center space-margin60 heading8">
                <h5>How We Work</h5>
                <div className="space18" />
                <h2 className="text-anime-style-3">We Create&nbsp;Digital Future</h2>
              </div>
            </Col>
          </Row>
          <Row>
            <Col lg={12}>
              <div className="work-widget-boxes">
                <Row>
                  {work.map((item, idx) => <Col lg={3} md={6} xs={6} key={idx} data-aos="zoom-in" data-aos-duration={800 + idx * 100}>
                      <div className="work-boxarea">
                        <h5>Step {item.number}</h5>
                        <div className="space20" />
                        <div className="icons">
                          <Image src={item.image} alt="" />
                        </div>
                        <div className="space24" />
                        <Link href="/service-single">{item.title}</Link>
                      </div>
                    </Col>)}
                </Row>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </>;
};
export default Features;