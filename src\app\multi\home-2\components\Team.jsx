import Footer1 from '@/assets/img/all-images/bg/footer-bg1.png';
import Team2 from '@/assets/img/all-images/bg/team-bg1.png';
import team4 from '@/assets/img/all-images/team/team-img4.png';
import team5 from '@/assets/img/all-images/team/team-img5.png';
import team6 from '@/assets/img/all-images/team/team-img6.png';
import team7 from '@/assets/img/all-images/team/team-img7.png';
import logo2 from '@/assets/img/icons/sub-logo2.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaFacebookF, FaInstagram, FaLinkedinIn, FaYoutube } from 'react-icons/fa6';
export const icons = [{
  url: '',
  icon: FaFacebookF
}, {
  url: '',
  icon: FaInstagram
}, {
  url: '',
  icon: FaYoutube
}, {
  url: '',
  icon: FaLinkedinIn
}];
export const member = [{
  image: team4,
  name: '<PERSON>',
  role: 'Founder - CEO'
}, {
  image: team5,
  name: 'Ayesha Khanam',
  role: 'Problem Solver'
}, {
  image: team6,
  name: 'Shakib Al Hasan',
  role: 'Problem Solver'
}, {
  image: team7,
  name: 'Sofia Hernandez',
  role: 'Problem Solver'
}];
const Team = () => {
  return <>
      <div className="team2-section-area sp2">
        <Container>
          <div className="team2-section-bg1" style={{
          backgroundImage: `url(${Footer1.src})`
        }}></div>
          <div className="team2-section-bg2" style={{
          backgroundImage: `url(${Team2.src})`
        }}></div>
          <Row>
            <Col lg={5} className="m-auto">
              <div className="team-header space-margin60 heading4 text-center">
                <h5>
                  <span>
                    <Image src={logo2} alt="" />
                  </span>
                  Our Team
                </h5>
                <div className="space20" />
                <h2 className="text-anime-style-3">Meet Our Expert Team</h2>
              </div>
            </Col>
          </Row>
          <Row>
            {member.map((item, idx) => <Col key={idx} lg={3} md={6} data-aos="zoom-in" data-aos-duration={800}>
                <div className="team-author-boxarea">
                  <div className="img1 image-anime">
                    <Image src={item.image} alt="" />
                  </div>
                  <div className="content-area">
                    <div className="text">
                      <Link href="/team">{item.name}</Link>
                      <div className="space8" />
                      <p>{item.role}</p>
                    </div>
                  </div>
                  <ul>
                    {icons.map((item, idx) => {
                  const Icon = item.icon;
                  return <li key={idx}>
                          <Link href="">
                            <Icon className="fa-brands" />
                          </Link>
                        </li>;
                })}
                  </ul>
                </div>
              </Col>)}
          </Row>
        </Container>
      </div>
    </>;
};
export default Team;