@use '../../utils/' as * ;

/*============= OTHERS CSS AREA STARTS ===============*/

// preloader //
.preloader{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999999999;
	background-color: var(--ztc-text-text-2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-container,
.loading{
	height: 140px;
	position: relative;
	width: 140px;
	border-radius: 100%;
}

.loading-container{
	margin: 40px auto
}

.loading{
	border: 1px solid transparent;
	border-color: transparent var(--ztc-text-text-1) transparent var(--ztc-text-text-1);
	animation: rotate-loading 1.5s linear 0s infinite normal;
	transform-origin: 50% 50%;
}
.loading-container:hover .loading,
.loading-container .loading{
	transition: all 0.5s ease-in-out;
}

#loading-icon{
	position: absolute;
	top: 48%;
	left: 50%;
	transform: translate(-50%, -50%);
    height: 80px;
    width: 70px;
}

@keyframes rotate-loading{
	0%{
		transform: rotate(0deg);
	}

	100%{
		transform: rotate(360deg);
	}
}

//  Page Progress Area //
.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
 }
 .progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
 }
 .progress-wrap::after {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  color: var(--ztc-text-text-2);
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 1;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
 }
 .progress-wrap:hover::after {
  opacity: 0;
  color: var(--ztc-text-text-2);
 }
 .progress-wrap::before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  opacity: 0;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 2;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
 }
 .progress-wrap:hover::before {
  opacity: 1;
 }
 .progress-wrap .svg-content path {
  fill: none;
 }
 .progress-wrap svg.progress-circle path {
  stroke: var(--ztc-text-text-2);
  stroke-width: 4;
  box-sizing: border-box;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
 }
 .progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
 }
 //  Page Progress Area //


 .others3-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-9);
  .elements29 {
    position: absolute;
    bottom: 0;
    right: 0;
  }
  .others-heading {
    h5 {
      img {
        filter: brightness(0) invert(1);
      }
    }
    .btn-area1 {
      a {
        background: var(--ztc-bg-bg-1);
        color: var(--ztc-text-text-9);
        &::after {
            background: var(--ztc-bg-bg-9);
            opacity: 10%;
        }
        &:hover {
            color: var(--ztc-text-text-1);
            transition: all .4s;
            &::after {
                background: var(--ztc-bg-bg-1);
                opacity: 10%;
            }
        }
    }
    }
  }
  .img1 {
    position: absolute;
    top: 0;
    @media #{$xs} {
      position: relative;
      margin-top: 30px;
    }
    @media #{$md} {
      position: relative;
    }
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
 }
/*============= OTHERS CSS AREA ENDS ===============*/
.pricingplan1-section-area {
  position: relative;
  z-index: 1;
  background: #EFF1FF;
  .elements30{
    position: absolute;
    top: 0;
    right: 0;
  }

  .toggle-inner input{
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    border-radius: 25px;
    right: 0;
    z-index: 1;
    opacity: 0;
    cursor: pointer;
  }

.custom-toggle {
  position: absolute;
  height: 20px;
  width: 20px;
  background-color: var(--ztc-text-text-1);
  top: 5px;
  left: 35px;
  border-radius: 50%;
  transition: 300ms all;
}

.toggle-inner .t-month,
.toggle-inner .t-year {
  position: absolute;
  left: -75px;
  top: 2px;
  transition: 300ms all;
}

.toggle-inner .t-year {
  left: unset;
  left: 73px;
  opacity: 0.5;
}

.active > .toggle-inner .t-month {
  opacity: 0.5;
}

.active > .toggle-inner .t-year {
  opacity: 1;
}

.toggle-inner input:checked + span {
  left: 5px;
}

.toggle-inner {
  width: 60px;
  margin: 0 auto;
  height: 30px;
  border-radius: 25px;
  position: relative;
  background: var(--ztc-bg-bg-9);
  left: -20px;
}

.t-year h4 {
  min-width: 200px;
}
.t-year {
  text-align: left;
}

.plan-toggle-wrap h4 {
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-7);
  font-family: "Figtree", sans-serif;
  margin-bottom: 0;
}

  .single-pricing-area {
    position: relative;
    z-index: 1;
    background: var(--ztc-bg-bg-1);
    border-radius: 8px;
    padding: 32px;
    .pricing-box {
      h3 {
        color: var(--ztc-text-text-7);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s20);
        font-style: normal;
        font-weight: var(--ztc-weight-semibold);
        line-height: 20px;
        text-transform: uppercase;      
      }
      p {
        color: var(--ztc-text-text-8);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        font-style: normal;
        font-weight: var(--ztc-weight-medium);
        line-height: 24px;  
      }
      ul {
        li {
          color: var(--ztc-text-text-8);
          font-family: var(--ztc-family-font1);
          font-size: var(--ztc-font-size-font-s16);
          font-style: normal;
          font-weight: var(--ztc-weight-medium);
          line-height: 16px;
          text-transform: capitalize;  
          margin-top: 16px;
          img {
            margin: 0 6px 0 0;
          }    
        }
      }
      h2 {
        color: var(--ztc-text-text-7);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        font-style: normal;
        font-weight: var(--ztc-weight-semibold);
        line-height: 54px; 
        span {
          font-size: var(--ztc-font-size-font-s16);
          display: inline-block;
          color: var(--ztc-text-text-8);
        }    
      }

      .btn-area1 {
        a {
          width: 100%;
          text-align: center;
          &::after {
            left: 26%;
          }
        }
      }
    }
  }
}

// Homepage 04 //
.pricing4-section-area {
  position: relative;
  z-index: 1;
  .pricing-single-boxarea {
    position: relative;
    z-index: 1;
    background: #F2F4FF;
    border-radius: 8px;
    padding: 42px 24px 32px 24px;
    margin-bottom: 30px;
    .price {
      z-index: 1;
      top: -80px;
      position: relative;
      left: 32px;
      h2 {
        color: var(--ztc-text-text-1);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        font-style: normal;
        font-weight: var(--ztc-weight-semibold);
        line-height: 44px;
        text-transform: capitalize;    
      }
      p {
        color: rgba(255, 255, 255, 0.80);
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        font-style: normal;
        font-weight: var(--ztc-weight-medium);
        line-height: 16px;
        text-transform: capitalize;     
      }
      img {
        position: absolute;
        top: -38px;
        z-index: -1;
        left: -42px;
        width: 160px;
        height: 160px;
        object-fit: contain;
        max-width: 160px;
        max-height: 160px;
      }
    }
    p {
      color: var(--ztc-text-text-11);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s18);
      font-style: normal;
      font-weight: var(--ztc-weight-medium);
      line-height: 18px;
      text-transform: capitalize;   
    }
    h3 {
      color: var(--ztc-text-text-10);
      font-family: var(--ztc-family-font1);
      font-size: var(--ztc-font-size-font-s32);
      font-style: normal;
      font-weight: var(--ztc-weight-semibold);
      line-height: 32px;
      text-transform: capitalize; 
      padding-bottom: 20px;
      border-bottom: 1px solid #DADCE8;   
    } 
    ul {
      li {
        margin-top: 16px;
        color:#333647;
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        font-style: normal;
        font-weight: var(--ztc-weight-medium);
        line-height: 16px;
        text-transform: capitalize;    
        img {
          margin: 0 6px 0 0;
        } 
      }
    }
    &.box2 {
      background: var(--ztc-bg-bg-10);
      p {
        color: var(--ztc-text-text-1);
      }
      h3 {
        color: var(--ztc-text-text-1);
      }
      ul {
        li {
          color: var(--ztc-text-text-1);
        }
      }
      .btn-area1 {
        a {
          span {
            background: var(--ztc-bg-bg-1);
            color: var(--ztc-text-text-12);
          }
        }
      }
    }
  
  }
}
/*============= PRICING CSS AREA STARTS ===============*/

/*============= PRICING CSS AREA ENDS ===============*/

/*============= CHOOSE CSS AREA STARTS ===============*/

/*============= CHOOSE CSS AREA ENDS ===============*/

/*============= ERROR CSS AREA ENDS ===============*/ 

/*============= ERROR CSS AREA ENDS ===============*/